# QBX Mining System

A complete, production-ready mining job system for the Qbox framework with first-class integration for ox_inventory, ox_lib, and ox_target.

## Features

### 🎯 Core Gameplay
- **Complete Mining Loop**: Mine → Smelt → Sell → Upgrade Tools → Level Up
- **50 Mining Levels** with configurable XP scaling and unlocks
- **6 Ore Types**: Coal, Copper, Iron, Silver, Gold, Diamond
- **5 Pickaxe Tiers**: Basic, Iron, Steel, Titanium, Diamond
- **3 Handle Types**: Wood, Iron, Titanium (with stat bonuses)

### 🎨 Modern UI
- **Dark Qbox-style Interface** using ox_lib context menus
- **Real-time Progress Tracking** with beautiful progress bars
- **Level Up Notifications** with visual effects
- **Tool Durability Display** with color-coded warnings
- **Smelting Queue Management** with persistent timers

### 🔒 Security & Anti-Exploit
- **Server-Authoritative Validation** for all rewards and actions
- **Distance & Line-of-Sight Checks** to prevent teleport exploits
- **Rate Limiting** to prevent spam attacks
- **Hash Whitelisting** for mining nodes
- **Durability Tracking** server-side to prevent item duplication

### 🏗️ Technical Excellence
- **Modular Architecture** with clean separation of concerns
- **Persistent Data Storage** for smelt queues and player progress
- **Performance Optimized** with configurable node limits and streaming
- **Fully Configurable** with extensive config options
- **Comprehensive Localization** support

## Installation

### Prerequisites
- **qbx_core** (Qbox Framework)
- **ox_lib** (v3.0.0+)
- **ox_inventory** (v2.40.0+)
- **ox_target** (v1.10.0+)

### Step 1: Download and Extract
1. Download the `qbx_mining` folder
2. Place it in your `resources` directory
3. Ensure the folder structure matches the layout below

### Step 2: Add Items to ox_inventory
The mining items have already been added to `ox_inventory/data/items.lua`. If you need to add them manually, they include:

**Raw Ores:**
- `coal_ore`, `copper_ore`, `iron_ore`, `silver_ore`, `gold_ore`, `diamond_ore`

**Refined Materials:**
- `coal`, `copper_ingot`, `iron_ingot`, `silver_ingot`, `gold_ingot`, `diamond_gem`

**Tools:**
- `pickaxe_basic`, `pickaxe_iron`, `pickaxe_steel`, `pickaxe_titanium`, `pickaxe_diamond`
- `handle_wood`, `handle_iron`, `handle_titanium`

### Step 3: Add Item Images
Copy the following 64x64 PNG images to `ox_inventory/web/images/`:

```
coal_ore.png          copper_ore.png         iron_ore.png
silver_ore.png         gold_ore.png           diamond_ore.png
coal.png               copper_ingot.png       iron_ingot.png
silver_ingot.png       gold_ingot.png         diamond_gem.png
pickaxe_basic.png      pickaxe_iron.png       pickaxe_steel.png
pickaxe_titanium.png   pickaxe_diamond.png
handle_wood.png        handle_iron.png        handle_titanium.png
```

### Step 4: Configure Server
Add to your `server.cfg`:
```
ensure qbx_mining
```

### Step 5: Database (Optional)
No database setup required! The system uses Qbox player metadata and file-based persistence.

## Configuration

### Basic Configuration
Edit `config.lua` to customize:

```lua
Config = {
    Debug = false,                    -- Enable debug mode
    Framework = 'qbox',              -- Framework type
    Locale = 'en',                   -- Language
    
    Quarry = {
        Center = vec3(2950.0, 2750.0, 43.2),  -- Mining area center
        MaxActiveNodes = 180,                   -- Performance limit
        -- ... more quarry settings
    },
    
    Progression = {
        MaxLevel = 50,                 -- Maximum mining level
        XPFormula = {                  -- XP calculation formula
            Base = 100,
            Exponent = 1.35,
            Linear = 25
        }
    }
}
```

### Advanced Configuration

#### Ore Layers
Configure mining layers with level requirements:
```lua
Levels = {
    { name='Coal Layer',    minLevel=1,  ores={'coal'},    zBand={43.0, 42.0} },
    { name='Copper Layer',  minLevel=10, ores={'copper'},  zBand={42.0, 40.5} },
    -- ... more layers
}
```

#### Tool Stats
Customize pickaxe and handle statistics:
```lua
Pickaxes = {
    { item='pickaxe_basic', baseDamage=1, speedMul=1.00, rareBoost=0, durability=150 },
    -- ... more pickaxes
}
```

#### Economy Settings
Configure prices and smelting:
```lua
Selling = {
    Prices = {
        coal=3, copper_ingot=25, iron_ingot=45, silver_ingot=80,
        gold_ingot=130, diamond_gem=600
    },
    PayTo = 'bank',  -- 'cash' or 'bank'
}
```

## Usage

### For Players

#### Getting Started
1. Visit the **Mining Vendor** at the quarry (GPS: 2944, 2746)
2. Purchase a **Basic Pickaxe** ($500)
3. Optionally buy a **Wooden Handle** ($250) for bonus stats
4. Find **Coal Nodes** (black markers) and start mining

#### Mining Process
1. **Target a Node**: Use ox_target to interact with ore nodes
2. **Mine the Ore**: Complete the progress bar and skill check
3. **Collect Rewards**: Receive raw ore and XP
4. **Level Up**: Unlock new ore types and better tools

#### Smelting
1. Visit the **Smelter NPC** (GPS: 1109, -2008)
2. Select ore type and amount to smelt
3. Provide coal as fuel (1 coal per 25 ore)
4. Wait for smelting to complete (1-5 minutes)

#### Selling
1. Visit the **Seller NPC** (GPS: 1091, -1998)
2. Choose individual items or "Sell All"
3. Receive payment in cash or bank account

### For Administrators

#### Admin Commands
```
/mining setlevel [playerid] [level]    - Set player's mining level
/mining addxp [playerid] [amount]      - Add XP to player
/mining regen                          - Regenerate all mining nodes
/mining debug                          - Toggle debug mode
```

#### Monitoring
- Check `qbx_mining/data/` for persistent data files
- Monitor server console for debug information
- Use `/mining debug` to visualize node information

## API Reference

### Server Exports
```lua
-- Get player's mining level
local level = exports.qbx_mining:GetPlayerMiningLevel(source)

-- Add XP to player
local success = exports.qbx_mining:AddMiningXP(source, amount)

-- Get all mining data
local data = exports.qbx_mining:GetMiningData()
```

### Client Exports
```lua
-- Start mining a specific node
exports.qbx_mining:StartMining(nodeId)

-- Get player's current level data
local levelData = exports.qbx_mining:GetPlayerLevel()

-- Check if player is currently mining
local isMining = exports.qbx_mining:IsCurrentlyMining()
```

### Events

#### Client to Server
```lua
-- Request to mine a node
TriggerServerEvent('qbx_mining:client:mineNode', nodeId, playerPos)

-- Purchase item from vendor
TriggerServerEvent('qbx_mining:client:purchaseItem', itemData, category)

-- Start smelting process
TriggerServerEvent('qbx_mining:client:startSmelt', oreType, amount)

-- Sell items
TriggerServerEvent('qbx_mining:client:sellItems', items)
```

#### Server to Client
```lua
-- Update node state
TriggerClientEvent('qbx_mining:server:nodeUpdate', source, nodeId, nodeData)

-- Update player level
TriggerClientEvent('qbx_mining:server:levelUpdate', source, levelData)

-- Sync all nodes
TriggerClientEvent('qbx_mining:server:syncNodes', source, nodes)
```

## File Structure

```
qbx_mining/
├── fxmanifest.lua              # Resource manifest
├── config.lua                  # Main configuration
├── README.md                   # This file
├── locales/
│   └── en.lua                  # English translations
├── shared/
│   ├── bridge.lua              # Framework bridge
│   └── types.lua               # Shared types and utilities
├── server/
│   ├── main.lua                # Main server logic
│   ├── mining.lua              # Mining mechanics
│   ├── economy.lua             # Economy system
│   ├── items.lua               # Item management
│   └── persistence.lua         # Data persistence
├── client/
│   ├── main.lua                # Main client logic
│   ├── mining.lua              # Mining interactions
│   ├── ui.lua                  # User interface
│   ├── peds.lua                # NPC management
│   └── models.lua              # Model and prop handling
├── web/
│   ├── index.html              # UI HTML
│   ├── style.css               # UI styling
│   └── app.js                  # UI JavaScript
└── data/                       # Persistent data (auto-created)
    ├── nodes.json              # Node states
    ├── smelt_queue.json        # Smelting jobs
    └── players.json            # Player backup data
```

## Troubleshooting

### Common Issues

#### "No mining nodes found"
- Check that the quarry center coordinates are correct
- Ensure nodes are being generated (check debug mode)
- Verify player is in the correct area

#### "Items not showing in inventory"
- Confirm all items are added to ox_inventory
- Check item names match exactly
- Restart ox_inventory after adding items

#### "Smelting not working"
- Verify player has enough coal
- Check smelting queue isn't full
- Ensure ox_inventory has space for output

#### "Tool durability not decreasing"
- Check server-side validation is working
- Verify item metadata is being saved
- Restart the resource if needed

### Debug Mode
Enable debug mode in config.lua:
```lua
Config.Debug = true
```

This will show:
- Node generation information
- Player level changes
- Item transactions
- Error messages

### Performance Issues
If experiencing lag:
1. Reduce `MaxActiveNodes` in config
2. Increase node respawn times
3. Reduce render distance for nodes
4. Check server console for errors

## Customization

### Adding New Ore Types
1. Add to `Config.Ores` and `Config.Ingots`
2. Create new layer in `Config.Quarry.Levels`
3. Add items to ox_inventory
4. Update localization files

### Custom Tool Tiers
1. Add new pickaxe to `Config.Tools.Pickaxes`
2. Set appropriate stats and requirements
3. Add item to ox_inventory
4. Create item image

### Modifying XP Formula
Adjust the XP formula in config:
```lua
XPFormula = {
    Base = 100,      -- Base XP requirement
    Exponent = 1.35, -- Exponential scaling
    Linear = 25      -- Linear component
}
```

Formula: `XP = floor(Base * (level^Exponent)) + Linear * level`

## Support

### Getting Help
1. Check this README thoroughly
2. Enable debug mode to diagnose issues
3. Check server console for error messages
4. Verify all dependencies are up to date

### Reporting Issues
When reporting issues, please include:
- FiveM server version
- Framework version (qbx_core)
- ox_lib, ox_inventory, ox_target versions
- Full error messages from console
- Steps to reproduce the issue

## License

This resource is provided as-is for educational and development purposes. Please respect the original authors and contributors.

## Credits

- **Framework**: Qbox Team
- **Dependencies**: Overextended (ox_lib, ox_inventory, ox_target)
- **Development**: BLACKBOXAI
- **Testing**: Community Contributors

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Compatibility**: Qbox Framework, ox_lib 3.0+, ox_inventory 2.40+, ox_target 1.10+
