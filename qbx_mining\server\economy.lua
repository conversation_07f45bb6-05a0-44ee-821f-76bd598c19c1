-- QBX Mining System - Economy Server
local locale = lib.locale

-- Process item purchase from vendor
function ProcessPurchase(src, itemData, category)
    local player = MiningData.players[src]
    if not player then return end
    
    local item, price, levelReq
    
    -- Determine item details based on category
    if category == 'Pickaxes' then
        for _, pickaxe in ipairs(Config.Tools.Pickaxes) do
            if pickaxe.item == itemData.item then
                item = pickaxe
                price = pickaxe.price
                levelReq = pickaxe.minLevel
                break
            end
        end
    elseif category == 'Handles' then
        for _, handle in ipairs(Config.Tools.Handles) do
            if handle.item == itemData.item then
                item = handle
                price = handle.price
                levelReq = handle.minLevel
                break
            end
        end
    elseif category == 'Supplies' then
        for _, supply in ipairs(Config.Vendor.Supplies) do
            if supply.item == itemData.item then
                item = supply
                price = supply.price
                levelReq = 1 -- Supplies have no level requirement
                break
            end
        end
    end
    
    if not item then
        Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
        return
    end
    
    -- Check level requirement
    if player.level < levelReq then
        Bridge.Notify(src, locale('insufficient_level'), Types.NotifyType.ERROR)
        return
    end
    
    -- Check if player has enough money
    local playerMoney = Bridge.GetMoney(src, 'cash')
    if playerMoney < price then
        Bridge.Notify(src, locale('insufficient_funds'), Types.NotifyType.ERROR)
        return
    end
    
    -- Check inventory space
    local canCarry = exports.ox_inventory:CanCarryItem(src, item.item, 1)
    if not canCarry then
        Bridge.Notify(src, locale('inventory_full'), Types.NotifyType.ERROR)
        return
    end
    
    -- Process purchase
    local success = Bridge.RemoveMoney(src, 'cash', price)
    if success then
        local metadata = {}
        
        -- Add durability for tools
        if category == 'Pickaxes' then
            metadata.durability = item.durability
        end
        
        local itemAdded = exports.ox_inventory:AddItem(src, item.item, 1, metadata)
        if itemAdded then
            Bridge.Notify(src, locale('purchase_success', item.label, price), Types.NotifyType.SUCCESS)
            
            if Config.Debug then
                print('^2[QBX Mining]^7 Player ' .. Bridge.GetPlayerName(src) .. ' purchased ' .. item.label .. ' for $' .. price)
            end
        else
            -- Refund if item couldn't be added
            Bridge.AddMoney(src, 'cash', price)
            Bridge.Notify(src, locale('inventory_full'), Types.NotifyType.ERROR)
        end
    else
        Bridge.Notify(src, locale('insufficient_funds'), Types.NotifyType.ERROR)
    end
end

-- Process smelting request
function ProcessSmelting(src, oreType, amount)
    local player = MiningData.players[src]
    if not player then return end
    
    -- Validate ore type
    if not Types.Utils.IsValidOreType(oreType) then
        Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
        return
    end
    
    -- Check amount limits
    if amount <= 0 or amount > Config.Smelting.MaxBatch then
        Bridge.Notify(src, locale('max_batch_exceeded', Config.Smelting.MaxBatch), Types.NotifyType.ERROR)
        return
    end
    
    -- Check if player has enough ore
    local oreItem = Config.Ores[oreType].item
    local oreCount = exports.ox_inventory:GetItemCount(src, oreItem)
    if oreCount < amount then
        Bridge.Notify(src, locale('insufficient_ore'), Types.NotifyType.ERROR)
        return
    end
    
    -- Check coal requirement
    local coalRequired = 0
    if Config.Smelting.RequiresCoal then
        coalRequired = Config.Smelting.CoalPerBatch(amount)
        local coalCount = exports.ox_inventory:GetItemCount(src, 'coal')
        if coalCount < coalRequired then
            Bridge.Notify(src, locale('insufficient_coal'), Types.NotifyType.ERROR)
            return
        end
    end
    
    -- Check smelt queue size
    local playerSmeltJobs = 0
    for _, job in pairs(MiningData.smeltQueue) do
        if job.playerId == src then
            playerSmeltJobs = playerSmeltJobs + 1
        end
    end
    
    if playerSmeltJobs >= Types.Constants.MAX_SMELT_QUEUE_SIZE then
        Bridge.Notify(src, locale('smelting_queue_full'), Types.NotifyType.ERROR)
        return
    end
    
    -- Calculate smelting time
    local smeltTime = math.max(
        Config.Smelting.MinSeconds,
        math.min(Config.Smelting.MaxSeconds, amount * Config.Smelting.TimePerOreSeconds)
    )
    
    -- Remove items from inventory
    local oreRemoved = exports.ox_inventory:RemoveItem(src, oreItem, amount)
    local coalRemoved = true
    
    if coalRequired > 0 then
        coalRemoved = exports.ox_inventory:RemoveItem(src, 'coal', coalRequired)
    end
    
    if oreRemoved and coalRemoved then
        -- Create smelt job
        local jobId = Types.Utils.GenerateSmeltId()
        local endTime = os.time() + smeltTime
        
        MiningData.smeltQueue[jobId] = {
            id = jobId,
            playerId = src,
            citizenid = Bridge.GetIdentifier(src),
            oreType = oreType,
            amount = amount,
            coalUsed = coalRequired,
            startTime = os.time(),
            endTime = endTime,
            state = Types.SmeltState.PROCESSING
        }
        
        -- Notify player
        Bridge.Notify(src, locale('smelting_in_progress'), Types.NotifyType.INFO)
        
        -- Update client with smelt progress
        TriggerClientEvent(Types.Events.SERVER_SMELT_UPDATE, src, {
            jobId = jobId,
            timeRemaining = smeltTime,
            state = Types.SmeltState.PROCESSING
        })
        
        -- Schedule completion
        SetTimeout(smeltTime * 1000, function()
            CompleteSmeltJob(jobId)
        end)
        
        if Config.Debug then
            print('^2[QBX Mining]^7 Started smelting job ' .. jobId .. ' for player ' .. Bridge.GetPlayerName(src))
        end
    else
        Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
    end
end

-- Complete smelting job
function CompleteSmeltJob(jobId)
    local job = MiningData.smeltQueue[jobId]
    if not job then return end
    
    local src = job.playerId
    
    -- Check if player is still online
    if not MiningData.players[src] then
        -- Player offline, mark job as completed for later collection
        job.state = Types.SmeltState.COMPLETED
        return
    end
    
    -- Get output item
    local outputItem = Config.Ingots[job.oreType].item
    local outputAmount = job.amount
    
    -- Try to give items to player
    local success = exports.ox_inventory:AddItem(src, outputItem, outputAmount)
    
    if success then
        -- Job completed successfully
        Bridge.Notify(src, locale('smelting_complete', outputAmount, Config.Ingots[job.oreType].label), Types.NotifyType.SUCCESS)
        
        -- Remove job from queue
        MiningData.smeltQueue[jobId] = nil
        
        -- Update client
        TriggerClientEvent(Types.Events.SERVER_SMELT_UPDATE, src, {
            jobId = jobId,
            state = Types.SmeltState.COMPLETED
        })
        
        if Config.Debug then
            print('^2[QBX Mining]^7 Completed smelting job ' .. jobId .. ' for player ' .. Bridge.GetPlayerName(src))
        end
    else
        -- Inventory full, mark as completed for later collection
        job.state = Types.SmeltState.COMPLETED
        Bridge.Notify(src, locale('inventory_full'), Types.NotifyType.WARNING)
    end
end

-- Process item sale
function ProcessSale(src, items)
    local player = MiningData.players[src]
    if not player then return end
    
    if not items or #items == 0 then
        Bridge.Notify(src, locale('nothing_to_sell'), Types.NotifyType.ERROR)
        return
    end
    
    local totalValue = 0
    local itemsToRemove = {}
    
    -- Calculate total value and validate items
    for _, saleItem in ipairs(items) do
        local itemName = saleItem.item
        local amount = saleItem.amount
        local price = Config.Selling.Prices[itemName]
        
        if price and amount > 0 then
            -- Check if player has the item
            local itemCount = exports.ox_inventory:GetItemCount(src, itemName)
            if itemCount >= amount then
                local itemValue = price * amount
                totalValue = totalValue + itemValue
                
                table.insert(itemsToRemove, {
                    item = itemName,
                    amount = amount,
                    value = itemValue
                })
            end
        end
    end
    
    if totalValue <= 0 then
        Bridge.Notify(src, locale('nothing_to_sell'), Types.NotifyType.ERROR)
        return
    end
    
    -- Calculate tax
    local tax = totalValue * Config.Selling.TaxRate
    local finalPayout = totalValue - tax
    
    -- Remove items from inventory
    local allRemoved = true
    for _, removeItem in ipairs(itemsToRemove) do
        local removed = exports.ox_inventory:RemoveItem(src, removeItem.item, removeItem.amount)
        if not removed then
            allRemoved = false
            break
        end
    end
    
    if allRemoved then
        -- Pay player
        local success = Bridge.AddMoney(src, Config.Selling.PayTo, math.floor(finalPayout))
        
        if success then
            Bridge.Notify(src, locale('sell_success', math.floor(finalPayout)), Types.NotifyType.SUCCESS)
            
            if Config.Debug then
                print('^2[QBX Mining]^7 Player ' .. Bridge.GetPlayerName(src) .. ' sold items for $' .. math.floor(finalPayout))
            end
        else
            -- Refund items if payment failed
            for _, removeItem in ipairs(itemsToRemove) do
                exports.ox_inventory:AddItem(src, removeItem.item, removeItem.amount)
            end
            Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
        end
    else
        Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
    end
end

-- Get player's smelt jobs
function GetPlayerSmeltJobs(src)
    local jobs = {}
    for jobId, job in pairs(MiningData.smeltQueue) do
        if job.playerId == src then
            local timeRemaining = math.max(0, job.endTime - os.time())
            table.insert(jobs, {
                id = jobId,
                oreType = job.oreType,
                amount = job.amount,
                timeRemaining = timeRemaining,
                state = job.state
            })
        end
    end
    return jobs
end

-- Clean up completed smelt jobs periodically
CreateThread(function()
    while true do
        Wait(60000) -- Check every minute
        
        local currentTime = os.time()
        local jobsToRemove = {}
        
        for jobId, job in pairs(MiningData.smeltQueue) do
            -- Remove completed jobs older than 1 hour
            if job.state == Types.SmeltState.COMPLETED and (currentTime - job.endTime) > 3600 then
                table.insert(jobsToRemove, jobId)
            end
        end
        
        for _, jobId in ipairs(jobsToRemove) do
            MiningData.smeltQueue[jobId] = nil
        end
        
        if Config.Debug and #jobsToRemove > 0 then
            print('^2[QBX Mining]^7 Cleaned up ' .. #jobsToRemove .. ' old smelt jobs')
        end
    end
end)

-- Price fluctuation system (if enabled)
if Config.Selling.PriceFluctuation.Enabled then
    CreateThread(function()
        while true do
            Wait(Config.Selling.PriceFluctuation.PeriodMinutes * 60000)
            
            -- Fluctuate prices
            for item, basePrice in pairs(Config.Selling.Prices) do
                local multiplier = math.random(
                    Config.Selling.PriceFluctuation.Min * 100,
                    Config.Selling.PriceFluctuation.Max * 100
                ) / 100
                
                Config.Selling.Prices[item] = math.floor(basePrice * multiplier)
            end
            
            if Config.Debug then
                print('^2[QBX Mining]^7 Updated selling prices due to market fluctuation')
            end
        end
    end)
end

-- Exports
exports('ProcessPurchase', ProcessPurchase)
exports('ProcessSmelting', ProcessSmelting)
exports('ProcessSale', ProcessSale)
exports('CompleteSmeltJob', CompleteSmeltJob)
exports('GetPlayerSmeltJobs', GetPlayerSmeltJobs)
