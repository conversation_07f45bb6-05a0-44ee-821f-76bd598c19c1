# QBX Mining System - Test Plan

## Overview
This document outlines comprehensive testing procedures for the QBX Mining System to ensure all features work correctly and securely.

## Pre-Test Setup

### Environment Requirements
- Fresh FiveM server with Qbox framework
- ox_lib, ox_inventory, ox_target installed and working
- qbx_mining resource installed with all dependencies
- Test player with admin permissions
- Clean player data (no existing mining progress)

### Test Data Preparation
1. Ensure all mining items are in ox_inventory
2. Verify item images are present
3. Check that NPCs spawn correctly
4. Confirm mining nodes are generated

## Test Categories

## 1. Basic Functionality Tests

### 1.1 Resource Loading
**Objective**: Verify resource loads without errors

**Steps**:
1. Start server with qbx_mining
2. Check console for errors
3. Verify all scripts load successfully
4. Confirm exports are available

**Expected Results**:
- No console errors
- Resource shows as "started"
- Debug messages appear if enabled
- All dependencies load correctly

**Pass Criteria**: ✅ Resource starts without errors

---

### 1.2 NPC Spawning
**Objective**: Verify all NPCs spawn and are interactive

**Steps**:
1. Navigate to vendor location (2944, 2746)
2. Navigate to smelter location (1109, -2008)
3. Navigate to seller location (1091, -1998)
4. Test ox_target interactions on each NPC

**Expected Results**:
- All 3 NPCs spawn correctly
- NPCs are frozen and invincible
- Target options appear when near NPCs
- Correct icons and labels display

**Pass Criteria**: ✅ All NPCs spawn and are interactive

---

### 1.3 Node Generation
**Objective**: Verify mining nodes generate correctly

**Steps**:
1. Enable debug mode: `/mining debug`
2. Navigate to quarry area (2950, 2750)
3. Observe node markers and debug info
4. Count nodes in each layer
5. Verify node colors match ore types

**Expected Results**:
- Nodes appear as colored markers
- Debug info shows node details
- Node count matches config
- Different ore types have different colors

**Pass Criteria**: ✅ Nodes generate according to configuration

---

## 2. Mining Mechanics Tests

### 2.1 Basic Mining (Level 1 Player)
**Objective**: Test basic mining functionality

**Steps**:
1. Create fresh player (Level 1)
2. Purchase Basic Pickaxe from vendor
3. Navigate to coal nodes
4. Target and mine a coal node
5. Complete mining animation and skill check

**Expected Results**:
- Can purchase basic pickaxe
- Coal nodes are targetable
- Mining animation plays
- Skill check appears
- Receive coal ore and XP on success

**Pass Criteria**: ✅ Basic mining works correctly

---

### 2.2 Level Restrictions
**Objective**: Verify level-gated content works

**Steps**:
1. Level 1 player attempts to mine copper (requires level 10)
2. Try to purchase iron pickaxe (requires level 10)
3. Use admin command to set level to 10
4. Retry copper mining and iron pickaxe purchase

**Expected Results**:
- Level 1 player cannot mine copper
- Level 1 player cannot buy iron pickaxe
- Level 10 player can access both
- Appropriate error messages display

**Pass Criteria**: ✅ Level restrictions work correctly

---

### 2.3 Tool Durability
**Objective**: Test tool durability system

**Steps**:
1. Purchase basic pickaxe (150 durability)
2. Mine nodes repeatedly
3. Monitor durability decrease
4. Continue until tool breaks
5. Verify tool is removed from inventory

**Expected Results**:
- Durability decreases with each hit
- UI shows durability warnings
- Tool breaks at 0 durability
- Broken tool is removed
- Player receives notification

**Pass Criteria**: ✅ Tool durability works correctly

---

### 2.4 XP and Leveling
**Objective**: Test progression system

**Steps**:
1. Fresh player mines coal (15 XP per ore)
2. Monitor XP gain notifications
3. Continue until level up occurs
4. Verify level up notification
5. Check new level unlocks

**Expected Results**:
- XP gained per ore mined
- Progress tracked correctly
- Level up notification appears
- New content unlocks at appropriate levels
- Metadata saves correctly

**Pass Criteria**: ✅ XP and leveling work correctly

---

## 3. Economy System Tests

### 3.1 Vendor Purchases
**Objective**: Test item purchasing system

**Steps**:
1. Player with $10,000 cash
2. Purchase each pickaxe tier (when level allows)
3. Purchase each handle type
4. Purchase supplies
5. Test insufficient funds scenario
6. Test insufficient level scenario

**Expected Results**:
- Items purchased successfully when requirements met
- Money deducted correctly
- Items added to inventory with correct metadata
- Error messages for insufficient funds/level
- UI updates correctly

**Pass Criteria**: ✅ Vendor purchases work correctly

---

### 3.2 Smelting System
**Objective**: Test ore smelting functionality

**Steps**:
1. Player with 100 coal ore and 10 coal
2. Start smelting 25 coal ore
3. Verify coal consumption (1 coal for 25 ore)
4. Wait for smelting completion
5. Test queue persistence (disconnect/reconnect)
6. Test inventory full scenario

**Expected Results**:
- Smelting starts with correct requirements
- Progress tracked in queue
- Coal consumed as fuel
- Ingots produced on completion
- Queue persists through disconnection
- Handles inventory full gracefully

**Pass Criteria**: ✅ Smelting system works correctly

---

### 3.3 Selling System
**Objective**: Test item selling functionality

**Steps**:
1. Player with various ingots
2. Sell individual items
3. Use "Sell All" function
4. Verify pricing calculations
5. Test tax application (if enabled)
6. Confirm payment method (cash/bank)

**Expected Results**:
- Individual sales work correctly
- Sell all processes multiple items
- Prices match configuration
- Tax calculated correctly
- Payment goes to correct account
- Items removed from inventory

**Pass Criteria**: ✅ Selling system works correctly

---

## 4. Security and Anti-Exploit Tests

### 4.1 Distance Validation
**Objective**: Test distance-based exploit prevention

**Steps**:
1. Player near mining node
2. Start mining animation
3. Move away during mining (>4m)
4. Attempt to complete mining
5. Try teleporting to distant node and mining

**Expected Results**:
- Mining stops when player moves too far
- Server rejects distant mining attempts
- Appropriate error messages
- No rewards given for invalid attempts

**Pass Criteria**: ✅ Distance validation works correctly

---

### 4.2 Rate Limiting
**Objective**: Test spam protection

**Steps**:
1. Rapidly click mining nodes
2. Attempt multiple simultaneous mining
3. Try to bypass client-side cooldowns
4. Monitor server response

**Expected Results**:
- Server enforces rate limits
- Excessive requests rejected
- No duplicate rewards
- Player receives rate limit warnings

**Pass Criteria**: ✅ Rate limiting works correctly

---

### 4.3 Item Validation
**Objective**: Test server-side item validation

**Steps**:
1. Attempt to mine without pickaxe
2. Try mining with broken tool
3. Test smelting without required items
4. Attempt to sell items not in inventory

**Expected Results**:
- All actions require proper items
- Server validates inventory contents
- Broken tools cannot be used
- Invalid requests rejected

**Pass Criteria**: ✅ Item validation works correctly

---

## 5. Performance Tests

### 5.1 Node Rendering Performance
**Objective**: Test performance with many nodes

**Steps**:
1. Set MaxActiveNodes to 500 in config
2. Generate nodes and measure FPS
3. Test with multiple players in area
4. Monitor memory usage
5. Test node streaming distance

**Expected Results**:
- Stable FPS with many nodes
- Memory usage remains reasonable
- Performance scales with player count
- Nodes stream in/out correctly

**Pass Criteria**: ✅ Performance remains acceptable

---

### 5.2 Persistence Performance
**Objective**: Test data saving/loading performance

**Steps**:
1. Generate large amounts of data
2. Force save operation
3. Restart resource
4. Measure load times
5. Test with corrupted data files

**Expected Results**:
- Save operations complete quickly
- Load times are reasonable
- Corrupted data handled gracefully
- No data loss during normal operation

**Pass Criteria**: ✅ Persistence performs well

---

## 6. Integration Tests

### 6.1 ox_inventory Integration
**Objective**: Test inventory system integration

**Steps**:
1. Test item addition/removal
2. Verify metadata handling
3. Test durability updates
4. Check item stacking
5. Test inventory full scenarios

**Expected Results**:
- Items integrate seamlessly
- Metadata preserved correctly
- Durability updates properly
- Stacking works as expected
- Full inventory handled gracefully

**Pass Criteria**: ✅ ox_inventory integration works correctly

---

### 6.2 ox_target Integration
**Objective**: Test targeting system integration

**Steps**:
1. Test node targeting
2. Test NPC targeting
3. Verify distance checks
4. Test multiple target options
5. Check target option conditions

**Expected Results**:
- All targets work correctly
- Distance checks function
- Options appear/disappear correctly
- Conditions evaluated properly
- No targeting conflicts

**Pass Criteria**: ✅ ox_target integration works correctly

---

### 6.3 Qbox Framework Integration
**Objective**: Test framework integration

**Steps**:
1. Test player data access
2. Verify metadata storage
3. Test money transactions
4. Check permission system
5. Test event handling

**Expected Results**:
- Player data accessible
- Metadata saves/loads correctly
- Money transactions work
- Permissions enforced
- Events fire properly

**Pass Criteria**: ✅ Qbox integration works correctly

---

## 7. Edge Case Tests

### 7.1 Disconnection Scenarios
**Objective**: Test behavior during disconnections

**Steps**:
1. Start mining, disconnect mid-animation
2. Start smelting, disconnect during process
3. Reconnect and verify state
4. Test data recovery

**Expected Results**:
- No data corruption
- Smelting continues after reconnect
- Player state restored correctly
- No duplicate rewards

**Pass Criteria**: ✅ Disconnection handling works correctly

---

### 7.2 Resource Restart Scenarios
**Objective**: Test resource restart behavior

**Steps**:
1. Active mining and smelting operations
2. Restart qbx_mining resource
3. Verify data persistence
4. Check player state recovery
5. Test ongoing operations

**Expected Results**:
- Data persists through restart
- Player states recovered
- Ongoing operations resume
- No data loss or corruption

**Pass Criteria**: ✅ Resource restart handling works correctly

---

### 7.3 Concurrent Player Tests
**Objective**: Test multiple players simultaneously

**Steps**:
1. 10+ players mining simultaneously
2. Multiple players at same node
3. Concurrent smelting operations
4. Simultaneous vendor interactions
5. Monitor for conflicts

**Expected Results**:
- No player conflicts
- All operations complete correctly
- Server performance remains stable
- Data integrity maintained

**Pass Criteria**: ✅ Concurrent operations work correctly

---

## 8. Admin Command Tests

### 8.1 Level Management
**Objective**: Test admin level commands

**Steps**:
1. `/mining setlevel [id] [level]` - valid player
2. `/mining setlevel [id] [level]` - invalid player
3. `/mining setlevel [id] [level]` - invalid level
4. Verify level changes take effect

**Expected Results**:
- Valid commands work correctly
- Invalid commands show errors
- Level changes apply immediately
- Player receives notifications

**Pass Criteria**: ✅ Level management commands work correctly

---

### 8.2 XP Management
**Objective**: Test admin XP commands

**Steps**:
1. `/mining addxp [id] [amount]` - valid values
2. `/mining addxp [id] [amount]` - invalid values
3. Test level up from XP addition
4. Verify XP changes persist

**Expected Results**:
- Valid XP additions work
- Invalid commands rejected
- Level ups trigger correctly
- Changes persist properly

**Pass Criteria**: ✅ XP management commands work correctly

---

### 8.3 System Management
**Objective**: Test system admin commands

**Steps**:
1. `/mining regen` - regenerate nodes
2. `/mining debug` - toggle debug mode
3. Verify commands work for admins only
4. Test command feedback

**Expected Results**:
- Node regeneration works
- Debug mode toggles correctly
- Non-admins cannot use commands
- Appropriate feedback provided

**Pass Criteria**: ✅ System management commands work correctly

---

## 9. Localization Tests

### 9.1 Language Support
**Objective**: Test localization system

**Steps**:
1. Verify all text uses locale system
2. Test missing translation handling
3. Check UI text updates
4. Test special characters

**Expected Results**:
- All text properly localized
- Missing translations handled gracefully
- UI updates with language changes
- Special characters display correctly

**Pass Criteria**: ✅ Localization works correctly

---

## 10. Final Integration Test

### 10.1 Complete Gameplay Loop
**Objective**: Test entire mining experience

**Steps**:
1. Fresh player joins server
2. Navigate to mining area
3. Purchase basic equipment
4. Mine coal to level 10
5. Upgrade to copper mining
6. Smelt ores to ingots
7. Sell ingots for profit
8. Purchase better equipment
9. Continue to higher levels
10. Test all ore types and tools

**Expected Results**:
- Complete loop works seamlessly
- Progression feels balanced
- No blocking bugs encountered
- Economy functions correctly
- Player experience is enjoyable

**Pass Criteria**: ✅ Complete gameplay loop works correctly

---

## Test Results Summary

| Test Category | Status | Notes |
|---------------|--------|-------|
| Basic Functionality | ⏳ Pending | |
| Mining Mechanics | ⏳ Pending | |
| Economy System | ⏳ Pending | |
| Security & Anti-Exploit | ⏳ Pending | |
| Performance | ⏳ Pending | |
| Integration | ⏳ Pending | |
| Edge Cases | ⏳ Pending | |
| Admin Commands | ⏳ Pending | |
| Localization | ⏳ Pending | |
| Final Integration | ⏳ Pending | |

**Legend:**
- ✅ Pass
- ❌ Fail
- ⚠️ Pass with Issues
- ⏳ Pending

## Bug Report Template

When reporting issues found during testing:

```
**Bug Title**: Brief description

**Severity**: Critical/High/Medium/Low

**Steps to Reproduce**:
1. Step one
2. Step two
3. Step three

**Expected Result**: What should happen

**Actual Result**: What actually happened

**Environment**:
- Server Version: 
- Framework Version:
- Resource Version:
- Dependencies:

**Additional Notes**: Any other relevant information
```

## Test Completion Checklist

- [ ] All test categories completed
- [ ] Critical bugs resolved
- [ ] Performance benchmarks met
- [ ] Security tests passed
- [ ] Integration tests successful
- [ ] Documentation updated
- [ ] Final approval obtained

---

**Test Plan Version**: 1.0  
**Last Updated**: 2024  
**Prepared By**: BLACKBOXAI  
**Review Status**: Pending
