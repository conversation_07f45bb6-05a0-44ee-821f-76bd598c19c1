-- QBX Mining System - Data Persistence
local locale = lib.locale

-- File paths for data storage
local DATA_PATH = GetResourcePath(GetCurrentResourceName()) .. '/data/'
local NODES_FILE = DATA_PATH .. 'nodes.json'
local SMELT_FILE = DATA_PATH .. 'smelt_queue.json'
local PLAYER_FILE = DATA_PATH .. 'players.json'

-- Ensure data directory exists
local function EnsureDataDirectory()
    -- Note: In FiveM, we can't create directories directly
    -- This would typically be handled by the server host
    if Config.Debug then
        print('^2[QBX Mining]^7 Data directory: ' .. DATA_PATH)
    end
end

-- Save data to JSON file
local function SaveToFile(filename, data)
    local success, result = pcall(function()
        local file = io.open(filename, 'w')
        if file then
            file:write(json.encode(data, { indent = true }))
            file:close()
            return true
        end
        return false
    end)
    
    if not success then
        if Config.Debug then
            print('^1[QBX Mining ERROR]^7 Failed to save to ' .. filename .. ': ' .. tostring(result))
        end
        return false
    end
    
    return result
end

-- Load data from JSON file
local function LoadFromFile(filename)
    local success, result = pcall(function()
        local file = io.open(filename, 'r')
        if file then
            local content = file:read('*all')
            file:close()
            if content and content ~= '' then
                return json.decode(content)
            end
        end
        return nil
    end)
    
    if not success then
        if Config.Debug then
            print('^3[QBX Mining WARNING]^7 Failed to load from ' .. filename .. ': ' .. tostring(result))
        end
        return nil
    end
    
    return result
end

-- Save mining nodes
function SaveNodes()
    if not Config.Quarry or not MiningData.nodes then
        return false
    end
    
    local nodeData = {
        timestamp = os.time(),
        seed = Config.Quarry.Seed,
        nodes = {}
    }
    
    -- Convert nodes to saveable format
    for nodeId, node in pairs(MiningData.nodes) do
        nodeData.nodes[nodeId] = {
            id = node.id,
            position = { x = node.position.x, y = node.position.y, z = node.position.z },
            oreType = node.oreType,
            health = node.health,
            maxHealth = node.maxHealth,
            state = node.state,
            minLevel = node.minLevel,
            respawnTime = node.respawnTime,
            lastHit = node.lastHit
        }
    end
    
    local success = SaveToFile(NODES_FILE, nodeData)
    
    if Config.Debug then
        if success then
            print('^2[QBX Mining]^7 Saved ' .. table.count(nodeData.nodes) .. ' nodes to file')
        else
            print('^1[QBX Mining ERROR]^7 Failed to save nodes')
        end
    end
    
    return success
end

-- Load mining nodes
function LoadNodes()
    local nodeData = LoadFromFile(NODES_FILE)
    
    if not nodeData or not nodeData.nodes then
        if Config.Debug then
            print('^3[QBX Mining]^7 No saved nodes found, will generate new ones')
        end
        return false
    end
    
    -- Check if seed has changed (regenerate if so)
    if nodeData.seed ~= Config.Quarry.Seed then
        if Config.Debug then
            print('^3[QBX Mining]^7 Seed changed, regenerating nodes')
        end
        return false
    end
    
    -- Convert loaded data back to node format
    MiningData.nodes = {}
    local loadedCount = 0
    
    for nodeId, nodeInfo in pairs(nodeData.nodes) do
        MiningData.nodes[nodeId] = {
            id = nodeInfo.id,
            position = vec3(nodeInfo.position.x, nodeInfo.position.y, nodeInfo.position.z),
            oreType = nodeInfo.oreType,
            health = nodeInfo.health,
            maxHealth = nodeInfo.maxHealth,
            state = nodeInfo.state,
            minLevel = nodeInfo.minLevel,
            respawnTime = nodeInfo.respawnTime or 0,
            lastHit = nodeInfo.lastHit or 0
        }
        loadedCount = loadedCount + 1
    end
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Loaded ' .. loadedCount .. ' nodes from file')
    end
    
    return true
end

-- Save smelt queue
function SaveSmeltQueue()
    if not MiningData.smeltQueue then
        return false
    end
    
    local smeltData = {
        timestamp = os.time(),
        queue = MiningData.smeltQueue
    }
    
    local success = SaveToFile(SMELT_FILE, smeltData)
    
    if Config.Debug then
        if success then
            print('^2[QBX Mining]^7 Saved ' .. table.count(MiningData.smeltQueue) .. ' smelt jobs to file')
        else
            print('^1[QBX Mining ERROR]^7 Failed to save smelt queue')
        end
    end
    
    return success
end

-- Load smelt queue
function LoadSmeltQueue()
    local smeltData = LoadFromFile(SMELT_FILE)
    
    if not smeltData or not smeltData.queue then
        if Config.Debug then
            print('^3[QBX Mining]^7 No saved smelt queue found')
        end
        MiningData.smeltQueue = {}
        return false
    end
    
    MiningData.smeltQueue = smeltData.queue
    local loadedCount = table.count(MiningData.smeltQueue)
    
    -- Restart timers for active smelt jobs
    local currentTime = os.time()
    local restarted = 0
    
    for jobId, job in pairs(MiningData.smeltQueue) do
        if job.state == Types.SmeltState.PROCESSING then
            local timeRemaining = job.endTime - currentTime
            
            if timeRemaining > 0 then
                -- Restart timer
                SetTimeout(timeRemaining * 1000, function()
                    exports.qbx_mining:CompleteSmeltJob(jobId)
                end)
                restarted = restarted + 1
            else
                -- Job should have completed, complete it now
                exports.qbx_mining:CompleteSmeltJob(jobId)
            end
        end
    end
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Loaded ' .. loadedCount .. ' smelt jobs, restarted ' .. restarted .. ' timers')
    end
    
    return true
end

-- Save player data (backup - primary storage is in metadata)
function SavePlayerData()
    if not MiningData.players then
        return false
    end
    
    local playerData = {
        timestamp = os.time(),
        players = {}
    }
    
    -- Save basic player data as backup
    for src, player in pairs(MiningData.players) do
        if player.citizenid then
            playerData.players[player.citizenid] = {
                level = player.level,
                xp = player.xp,
                currentPickaxe = player.currentPickaxe,
                currentHandle = player.currentHandle,
                lastSeen = os.time()
            }
        end
    end
    
    local success = SaveToFile(PLAYER_FILE, playerData)
    
    if Config.Debug then
        if success then
            print('^2[QBX Mining]^7 Saved ' .. table.count(playerData.players) .. ' player records to file')
        else
            print('^1[QBX Mining ERROR]^7 Failed to save player data')
        end
    end
    
    return success
end

-- Load player data (backup - primary storage is in metadata)
function LoadPlayerData()
    local playerData = LoadFromFile(PLAYER_FILE)
    
    if not playerData or not playerData.players then
        if Config.Debug then
            print('^3[QBX Mining]^7 No saved player data found')
        end
        return false
    end
    
    -- This data is mainly for reference/backup
    -- Primary data comes from player metadata
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Loaded ' .. table.count(playerData.players) .. ' player records from file')
    end
    
    return true
end

-- Save all persistent data
function SavePersistentData()
    if not Config.Smelting.QueuePersist then
        return
    end
    
    local startTime = GetGameTimer()
    
    -- Save in parallel
    CreateThread(function()
        SaveNodes()
    end)
    
    CreateThread(function()
        SaveSmeltQueue()
    end)
    
    CreateThread(function()
        SavePlayerData()
    end)
    
    MiningData.lastSave = os.time()
    
    if Config.Debug then
        local elapsed = GetGameTimer() - startTime
        print('^2[QBX Mining]^7 Saved persistent data in ' .. elapsed .. 'ms')
    end
end

-- Load all persistent data
function LoadPersistentData()
    if not Config.Smelting.QueuePersist then
        return
    end
    
    local startTime = GetGameTimer()
    
    EnsureDataDirectory()
    
    -- Load data
    local nodesLoaded = LoadNodes()
    local smeltLoaded = LoadSmeltQueue()
    local playersLoaded = LoadPlayerData()
    
    if Config.Debug then
        local elapsed = GetGameTimer() - startTime
        print('^2[QBX Mining]^7 Loaded persistent data in ' .. elapsed .. 'ms')
        print('^2[QBX Mining]^7 Nodes: ' .. (nodesLoaded and 'loaded' or 'not found'))
        print('^2[QBX Mining]^7 Smelt queue: ' .. (smeltLoaded and 'loaded' or 'not found'))
        print('^2[QBX Mining]^7 Players: ' .. (playersLoaded and 'loaded' or 'not found'))
    end
    
    return nodesLoaded, smeltLoaded, playersLoaded
end

-- Clean up old data files
function CleanupOldData()
    local currentTime = os.time()
    local maxAge = 7 * 24 * 60 * 60 -- 7 days in seconds
    
    -- This would typically involve checking file timestamps
    -- and removing files older than maxAge
    -- Implementation depends on server environment
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Cleanup completed')
    end
end

-- Backup data
function BackupData()
    local timestamp = os.date('%Y%m%d_%H%M%S')
    local backupPath = DATA_PATH .. 'backups/'
    
    -- Create backup copies with timestamp
    -- Implementation depends on server environment
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Data backed up with timestamp: ' .. timestamp)
    end
end

-- Data integrity check
function ValidateDataIntegrity()
    local issues = {}
    
    -- Check nodes
    if MiningData.nodes then
        for nodeId, node in pairs(MiningData.nodes) do
            if not node.id or not node.position or not node.oreType then
                table.insert(issues, 'Invalid node: ' .. nodeId)
            end
        end
    end
    
    -- Check smelt queue
    if MiningData.smeltQueue then
        for jobId, job in pairs(MiningData.smeltQueue) do
            if not job.id or not job.playerId or not job.oreType then
                table.insert(issues, 'Invalid smelt job: ' .. jobId)
            end
        end
    end
    
    if #issues > 0 then
        if Config.Debug then
            print('^1[QBX Mining ERROR]^7 Data integrity issues found:')
            for _, issue in ipairs(issues) do
                print('^1  - ' .. issue .. '^7')
            end
        end
        return false
    end
    
    return true
end

-- Periodic data maintenance
CreateThread(function()
    while true do
        Wait(3600000) -- Every hour
        
        -- Validate data integrity
        ValidateDataIntegrity()
        
        -- Clean up old data
        CleanupOldData()
        
        -- Create backup (if enabled)
        if Config.Debug then
            BackupData()
        end
    end
end)

-- Exports
exports('SavePersistentData', SavePersistentData)
exports('LoadPersistentData', LoadPersistentData)
exports('SaveNodes', SaveNodes)
exports('LoadNodes', LoadNodes)
exports('SaveSmeltQueue', SaveSmeltQueue)
exports('LoadSmeltQueue', LoadSmeltQueue)
exports('SavePlayerData', SavePlayerData)
exports('LoadPlayerData', LoadPlayerData)
exports('ValidateDataIntegrity', ValidateDataIntegrity)
