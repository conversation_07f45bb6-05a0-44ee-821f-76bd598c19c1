return {
	['testburger'] = {
		label = 'Test Burger',
		weight = 220,
		degrade = 60,
		client = {
			image = 'burger_chicken.png',
			status = { hunger = 200000 },
			anim = 'eating',
			prop = 'burger',
			usetime = 2500,
			export = 'ox_inventory_examples.testburger'
		},
		server = {
			export = 'ox_inventory_examples.testburger',
			test = 'what an amazingly delicious burger, amirite?'
		},
		buttons = {
			{
				label = 'Lick it',
				action = function(slot)
					print('You licked the burger')
				end
			},
			{
				label = 'Squeeze it',
				action = function(slot)
					print('You squeezed the burger :(')
				end
			},
			{
				label = 'What do you call a vegan burger?',
				group = 'Hamburger Puns',
				action = function(slot)
					print('A misteak.')
				end
			},
			{
				label = 'What do frogs like to eat with their hamburgers?',
				group = 'Hamburger Puns',
				action = function(slot)
					print('French flies.')
				end
			},
			{
				label = 'Why were the burger and fries running?',
				group = 'Hamburger Puns',
				action = function(slot)
					print('Because they\'re fast food.')
				end
			}
		},
		consume = 0.3
	},

	['bandage'] = {
		label = 'Bandage',
		weight = 115,
		client = {
			anim = { dict = 'missheistdockssetup1clipboard@idle_a', clip = 'idle_a', flag = 49 },
			usetime = 2500,
		}
	},

	['black_money'] = {
		label = 'Dirty Money',
	},

	['burger'] = {
		label = 'Burger',
		weight = 220,
		client = {
			status = { hunger = 200000 },
			anim = 'eating',
			prop = 'burger',
			usetime = 2500,
			notification = 'You ate a delicious burger'
		},
	},

	['sprunk'] = {
		label = 'Sprunk',
		weight = 350,
		client = {
			status = { thirst = 200000 },
			anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
			prop = { model = `prop_ld_can_01`, pos = vec3(0.01, 0.01, 0.06), rot = vec3(5.0, 5.0, -180.5) },
			usetime = 2500,
			notification = 'You quenched your thirst with a sprunk'
		}
	},

	['parachute'] = {
		label = 'Parachute',
		weight = 8000,
		stack = false,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		}
	},

	['garbage'] = {
		label = 'Garbage',
	},

	['paperbag'] = {
		label = 'Paper Bag',
		weight = 1,
		stack = false,
		close = false,
		consume = 0
	},

	['identification'] = {
		label = 'Identification',
		client = {
			image = 'card_id.png'
		}
	},

	['panties'] = {
		label = 'Knickers',
		weight = 10,
		consume = 0,
		client = {
			status = { thirst = -100000, stress = -25000 },
			anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
			prop = { model = `prop_cs_panties_02`, pos = vec3(0.03, 0.0, 0.02), rot = vec3(0.0, -13.5, -1.5) },
			usetime = 2500,
		}
	},

	['lockpick'] = {
		label = 'Lockpick',
		weight = 160,
	},

	['phone'] = {
		label = 'Phone',
		weight = 190,
		stack = false,
		consume = 0,
		client = {
			add = function(total)
				if total > 0 then
					pcall(function() return exports.npwd:setPhoneDisabled(false) end)
				end
			end,

			remove = function(total)
				if total < 1 then
					pcall(function() return exports.npwd:setPhoneDisabled(true) end)
				end
			end
		}
	},

	['money'] = {
		label = 'Money',
	},

	['mustard'] = {
		label = 'Mustard',
		weight = 500,
		client = {
			status = { hunger = 25000, thirst = 25000 },
			anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
			prop = { model = `prop_food_mustard`, pos = vec3(0.01, 0.0, -0.07), rot = vec3(1.0, 1.0, -1.5) },
			usetime = 2500,
			notification = 'You.. drank mustard'
		}
	},

	['water'] = {
		label = 'Water',
		weight = 500,
		client = {
			status = { thirst = 200000 },
			anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
			prop = { model = `prop_ld_flow_bottle`, pos = vec3(0.03, 0.03, 0.02), rot = vec3(0.0, 0.0, -1.5) },
			usetime = 2500,
			cancel = true,
			notification = 'You drank some refreshing water'
		}
	},

	['radio'] = {
		label = 'Radio',
		weight = 1000,
		stack = false,
		allowArmed = true
	},

	['armour'] = {
		label = 'Bulletproof Vest',
		weight = 3000,
		stack = false,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 3500
		}
	},

	-- Item Definitions for shared/items.lua
	['armour_light'] = {
		label = 'Light Vest',
		weight = 1500,
		stack = false,
		description = 'A lightweight protective vest offering basic protection against small arms fire. Provides 25% armor coverage.',
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 2500
		}
	},

	['armour_medium'] = {
		label = 'Tactical Vest',
		weight = 2500,
		stack = false,
		description = 'A tactical vest with MOLLE webbing and moderate protection. Standard issue for law enforcement. Provides 50% armor coverage.',
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 3000
		}
	},

	['armour_heavy'] = {
		label = 'Heavy Vest',
		weight = 4000,
		stack = false,
		description = 'A heavy-duty plate carrier vest with reinforced armor plating. Used by special forces and SWAT teams. Provides 75% armor coverage.',
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 4000
		}
	},

	['armour_military'] = {
		label = 'Military Vest',
		weight = 5000,
		stack = false,
		description = 'Military-grade tactical armor with maximum protection plates and comprehensive coverage. Top-tier battlefield protection. Provides 100% armor coverage.',
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 5000
		}
	},
	['clothing'] = {
		label = 'Clothing',
		consume = 0,
	},

	['mastercard'] = {
		label = 'Fleeca Card',
		stack = false,
		weight = 10,
		client = {
			image = 'card_bank.png'
		}
	},

	['scrapmetal'] = {
		label = 'Scrap Metal',
		weight = 80,
	},

	["crack_baggy"] = {
		label = "Bag of Crack",
		weight = 0,
		stack = true,
		close = true,
		description = "To get happy faster",
		client = {
			image = "crack_baggy.png",
		}
	},

	["weed_brick"] = {
		label = "Weed Brick",
		weight = 1000,
		stack = true,
		close = true,
		description = "1KG Weed Brick to sell to large customers.",
		client = {
			image = "weed_brick.png",
		}
	},

	["newsbmic"] = {
		label = "Boom Microphone",
		weight = 100,
		stack = false,
		close = true,
		description = "A Useable BoomMic",
		client = {
			image = "newsbmic.png",
		}
	},

	["veh_exterior"] = {
		label = "Exterior",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle exterior",
		client = {
			image = "veh_exterior.png",
		}
	},

	["nvscope_attachment"] = {
		label = "Night Vision Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A night vision scope for a weapon",
		client = {
			image = "nvscope_attachment.png",
		}
	},

	["jerry_can"] = {
		label = "Jerrycan 20L",
		weight = 20000,
		stack = true,
		close = true,
		description = "A can full of Fuel",
		client = {
			image = "jerry_can.png",
		}
	},

	["weed_amnesia_seed"] = {
		label = "Amnesia Seed",
		weight = 0,
		stack = true,
		close = true,
		description = "A weed seed of Amnesia",
		client = {
			image = "weed_seed.png",
		}
	},

	["handcuffs"] = {
		label = "Handcuffs",
		weight = 100,
		stack = true,
		close = true,
		description = "Comes in handy when people misbehave. Maybe it can be used for something else?",
		client = {
			image = "handcuffs.png",
		}
	},

	["smallscope_attachment"] = {
		label = "Small Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A small scope for a weapon",
		client = {
			image = "smallscope_attachment.png",
		}
	},

	["joint"] = {
		label = "Joint",
		weight = 0,
		stack = true,
		close = true,
		description = "Sidney would be very proud at you",
		client = {
			image = "joint.png",
		}
	},

	["armor"] = {
		label = "Armor",
		weight = 5000,
		stack = true,
		close = true,
		description = "Some protection won't hurt... right?",
		client = {
			image = "armor.png",
		}
	},

	["moneybag"] = {
		label = "Money Bag",
		weight = 0,
		stack = false,
		close = true,
		description = "A bag with cash",
		client = {
			image = "moneybag.png",
		}
	},

	["shitgpu"] = {
		label = "A trash gpu",
		weight = 0,
		stack = false,
		close = true,
		description = "Just look at it, what do you expect!",
		client = {
			image = "shitgpu.png",
		}
	},

	["drill"] = {
		label = "Drill",
		weight = 20000,
		stack = true,
		close = false,
		description = "The real deal...",
		client = {
			image = "drill.png",
		}
	},

	["clip_attachment"] = {
		label = "Clip",
		weight = 1000,
		stack = true,
		close = true,
		description = "A clip for a weapon",
		client = {
			image = "clip_attachment.png",
		}
	},

	["cokebaggy"] = {
		label = "Bag of Coke",
		weight = 0,
		stack = true,
		close = true,
		description = "To get happy real quick",
		client = {
			image = "cocaine_baggy.png",
		}
	},

	["heist_papers"] = {
		label = "Vehicle Papers",
		weight = 0,
		stack = false,
		close = true,
		description = "Delivery documents.",
		client = {
			image = "heist_papers.png",
		}
	},

	["nitrous"] = {
		label = "Nitrous",
		weight = 1000,
		stack = true,
		close = true,
		description = "Speed up, gas pedal! :D",
		client = {
			image = "nitrous.png",
		}
	},

	["wine"] = {
		label = "Wine",
		weight = 300,
		stack = true,
		close = false,
		description = "Some good wine to drink on a fine evening",
		client = {
			image = "wine.png",
		}
	},

	["radioscanner"] = {
		label = "Radio Scanner",
		weight = 1000,
		stack = true,
		close = true,
		description = "With this you can get some police alerts. Not 100% effective however",
		client = {
			image = "radioscanner.png",
		}
	},

	["thermite"] = {
		label = "Thermite",
		weight = 1000,
		stack = true,
		close = true,
		description = "Sometimes you'd wish for everything to burn",
		client = {
			image = "thermite.png",
		}
	},

	["diving_fill"] = {
		label = "Diving Tube",
		weight = 3000,
		stack = false,
		close = true,
		description = "An oxygen tube and a rebreather",
		client = {
			image = "diving_tube.png",
		}
	},

	["diving_gear"] = {
		label = "Diving Gear",
		weight = 30000,
		stack = false,
		close = true,
		description = "An oxygen tank and a rebreather",
		client = {
			image = "diving_gear.png",
		}
	},

	["tactical_muzzle_brake"] = {
		label = "Tactical Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brakee for a weapon",
		client = {
			image = "tactical_muzzle_brake.png",
		}
	},

	["meth"] = {
		label = "Meth",
		weight = 100,
		stack = true,
		close = true,
		description = "A baggie of Meth",
		client = {
			image = "meth_baggy.png",
		}
	},

	["firework3"] = {
		label = "WipeOut",
		weight = 1000,
		stack = true,
		close = true,
		description = "Fireworks",
		client = {
			image = "firework3.png",
		}
	},

	["copper"] = {
		label = "Copper",
		weight = 100,
		stack = true,
		close = false,
		description = "Nice piece of metal that you can probably use for something",
		client = {
			image = "copper.png",
		}
	},

	["boomcamo_attachment"] = {
		label = "Boom Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A boom camo for a weapon",
		client = {
			image = "boomcamo_attachment.png",
		}
	},

	["security_card_01"] = {
		label = "Security Card A",
		weight = 0,
		stack = true,
		close = true,
		description = "A security card... I wonder what it goes to",
		client = {
			image = "security_card_01.png",
		}
	},

	["glass"] = {
		label = "Glass",
		weight = 100,
		stack = true,
		close = false,
		description = "It is very fragile, watch out",
		client = {
			image = "glass.png",
		}
	},

	["veh_suspension"] = {
		label = "Suspension",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle suspension",
		client = {
			image = "veh_suspension.png",
		}
	},

	["whiskey"] = {
		label = "Whiskey",
		weight = 500,
		stack = true,
		close = true,
		description = "For all the thirsty out there",
		client = {
			image = "whiskey.png",
		}
	},

	["weed_purplehaze_seed"] = {
		label = "Purple Haze Seed",
		weight = 0,
		stack = true,
		close = true,
		description = "A weed seed of Purple Haze",
		client = {
			image = "weed_seed.png",
		}
	},

	["digicamo_attachment"] = {
		label = "Digital Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A digital camo for a weapon",
		client = {
			image = "digicamo_attachment.png",
		}
	},

	["tunerlaptop"] = {
		label = "Tunerchip",
		weight = 2000,
		stack = false,
		close = true,
		description = "With this tunerchip you can get your car on steroids... If you know what you're doing",
		client = {
			image = "tunerchip.png",
		}
	},

	["geocamo_attachment"] = {
		label = "Geometric Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A geometric camo for a weapon",
		client = {
			image = "geocamo_attachment.png",
		}
	},

	["rolex"] = {
		label = "Golden Watch",
		weight = 150,
		stack = true,
		close = true,
		description = "A golden watch seems like the jackpot to me!",
		client = {
			image = "rolex.png",
		}
	},

	["weaponlicense"] = {
		label = "Weapon License",
		weight = 0,
		stack = false,
		close = true,
		description = "Weapon License",

	},

	["xtcbaggy"] = {
		label = "Bag of XTC",
		weight = 0,
		stack = true,
		close = true,
		description = "Pop those pills baby",
		client = {
			image = "xtc_baggy.png",
		}
	},

	["vodka"] = {
		label = "Vodka",
		weight = 500,
		stack = true,
		close = true,
		description = "For all the thirsty out there",
		client = {
			image = "vodka.png",
		}
	},

	["holoscope_attachment"] = {
		label = "Holo Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A holo scope for a weapon",
		client = {
			image = "holoscope_attachment.png",
		}
	},

	["weed_ak47_seed"] = {
		label = "AK47 Seed",
		weight = 0,
		stack = true,
		close = true,
		description = "A weed seed of AK47",
		client = {
			image = "weed_seed.png",
		}
	},

	["laptop"] = {
		label = "Laptop",
		weight = 4000,
		stack = true,
		close = true,
		description = "Expensive laptop",
		client = {
			image = "laptop.png",
		}
	},

	["repairkit"] = {
		label = "Repairkit",
		weight = 2500,
		stack = true,
		close = true,
		description = "A nice toolbox with stuff to repair your vehicle",
		client = {
			image = "repairkit.png",
		}
	},

	["tablet"] = {
		label = "Tablet",
		weight = 2000,
		stack = true,
		close = true,
		description = "Expensive tablet",
		client = {
			image = "tablet.png",
		}
	},

	["lighter"] = {
		label = "Lighter",
		weight = 0,
		stack = true,
		close = true,
		description = "On new years eve a nice fire to stand next to",
		client = {
			image = "lighter.png",
		}
	},

	["racing_gps"] = {
		label = "Racing GPS",
		weight = 500,
		stack = false,
		close = true,
		description = "Wroom wroom.",
		client = {
			image = "racing_gps.png",
		}
	},

	["4090gpu"] = {
		label = "RTX 4090",
		weight = 0,
		stack = false,
		close = true,
		description = "Is it just me or this looks kinda thicc?",
		client = {
			image = "4090.png",
		}
	},

	["beer"] = {
		label = "Beer",
		weight = 500,
		stack = true,
		close = true,
		description = "Nothing like a good cold beer!",
		client = {
			image = "beer.png",
		}
	},

	["painkillers"] = {
		label = "Painkillers",
		weight = 0,
		stack = true,
		close = true,
		description = "For pain you can't stand anymore, take this pill that'd make you feel great again",
		client = {
			image = "painkillers.png",
		}
	},

	["2080gpu"] = {
		label = "RTX 2080",
		weight = 0,
		stack = false,
		close = true,
		description = "WOOW!! A new look! Then This is the one.",
		client = {
			image = "2080.png",
		}
	},

	["security_card_02"] = {
		label = "Security Card B",
		weight = 0,
		stack = true,
		close = true,
		description = "A security card... I wonder what it goes to",
		client = {
			image = "security_card_02.png",
		}
	},

	["veh_turbo"] = {
		label = "Turbo",
		weight = 1000,
		stack = true,
		close = true,
		description = "Install vehicle turbo",
		client = {
			image = "veh_turbo.png",
		}
	},

	["veh_xenons"] = {
		label = "Xenons",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle xenons",
		client = {
			image = "veh_xenons.png",
		}
	},

	["empty_evidence_bag"] = {
		label = "Empty Evidence Bag",
		weight = 0,
		stack = true,
		close = false,
		description = "Used a lot to keep DNA from blood, bullet shells and more",
		client = {
			image = "evidence.png",
		}
	},

	["1080gpu"] = {
		label = "GTX 1080",
		weight = 0,
		stack = false,
		close = true,
		description = "It must be good since it got 3 fans right?",
		client = {
			image = "1080.png",
		}
	},

	["advancedlockpick"] = {
		label = "Advanced Lockpick",
		weight = 500,
		stack = true,
		close = true,
		description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
		client = {
			image = "advancedlockpick.png",
		}
	},

	["heavy_duty_muzzle_brake"] = {
		label = "HD Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "heavy_duty_muzzle_brake.png",
		}
	},

	["goldbar"] = {
		label = "Gold Bar",
		weight = 7000,
		stack = true,
		close = true,
		description = "Looks pretty expensive to me",
		client = {
			image = "goldbar.png",
		}
	},

	["veh_interior"] = {
		label = "Interior",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle interior",
		client = {
			image = "veh_interior.png",
		}
	},

	["veh_wheels"] = {
		label = "Wheels",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle wheels",
		client = {
			image = "veh_wheels.png",
		}
	},

	["heavyarmor"] = {
		label = "Heavy Armor",
		weight = 5000,
		stack = true,
		close = true,
		description = "Some protection won't hurt... right?",
		client = {
			image = "armor.png",
		}
	},

	["ironoxide"] = {
		label = "Iron Powder",
		weight = 100,
		stack = true,
		close = false,
		description = "Some powder to mix with.",
		client = {
			image = "ironoxide.png",
		}
	},

	["coke_small_brick"] = {
		label = "Coke Package",
		weight = 350,
		stack = false,
		close = true,
		description = "Small package of cocaine, mostly used for deals and takes a lot of space",
		client = {
			image = "coke_small_brick.png",
		}
	},

	["patriotcamo_attachment"] = {
		label = "Patriot Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A patriot camo for a weapon",
		client = {
			image = "patriotcamo_attachment.png",
		}
	},

	["printerdocument"] = {
		label = "Document",
		weight = 500,
		stack = false,
		close = true,
		description = "A nice document",
		client = {
			image = "printerdocument.png",
		}
	},

	["aluminum"] = {
		label = "Aluminium",
		weight = 100,
		stack = true,
		close = false,
		description = "Nice piece of metal that you can probably use for something",
		client = {
			image = "aluminum.png",
		}
	},

	["rolling_paper"] = {
		label = "Rolling Paper",
		weight = 0,
		stack = true,
		close = true,
		description = "Paper made specifically for encasing and smoking tobacco or cannabis.",
		client = {
			image = "rolling_paper.png",
		}
	},

	["brushcamo_attachment"] = {
		label = "Brushstroke Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A brushstroke camo for a weapon",
		client = {
			image = "brushcamo_attachment.png",
		}
	},

	["fat_end_muzzle_brake"] = {
		label = "Fat End Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "fat_end_muzzle_brake.png",
		}
	},

	["sandwich"] = {
		label = "Sandwich",
		weight = 200,
		stack = true,
		close = true,
		description = "Nice bread for your stomach",
		client = {
			image = "sandwich.png",
		}
	},

	["walkstick"] = {
		label = "Walking Stick",
		weight = 1000,
		stack = true,
		close = true,
		description = "Walking stick for ya'll grannies out there.. HAHA",
		client = {
			image = "walkstick.png",
		}
	},

	["aluminumoxide"] = {
		label = "Aluminium Powder",
		weight = 100,
		stack = true,
		close = false,
		description = "Some powder to mix with",
		client = {
			image = "aluminumoxide.png",
		}
	},

	["slanted_muzzle_brake"] = {
		label = "Slanted Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "slanted_muzzle_brake.png",
		}
	},

	["thermalscope_attachment"] = {
		label = "Thermal Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A thermal scope for a weapon",
		client = {
			image = "thermalscope_attachment.png",
		}
	},

	["weed_whitewidow_seed"] = {
		label = "White Widow Seed",
		weight = 0,
		stack = true,
		close = false,
		description = "A weed seed of White Widow",
		client = {
			image = "weed_seed.png",
		}
	},

	["firework1"] = {
		label = "2Brothers",
		weight = 1000,
		stack = true,
		close = true,
		description = "Fireworks",
		client = {
			image = "firework1.png",
		}
	},

	["fitbit"] = {
		label = "Fitbit",
		weight = 500,
		stack = false,
		close = true,
		description = "I like fitbit",
		client = {
			image = "fitbit.png",
		}
	},

	["veh_tint"] = {
		label = "Tints",
		weight = 1000,
		stack = true,
		close = true,
		description = "Install vehicle tint",
		client = {
			image = "veh_tint.png",
		}
	},

	["lawyerpass"] = {
		label = "Lawyer Pass",
		weight = 0,
		stack = false,
		close = false,
		description = "Pass exclusive to lawyers to show they can represent a suspect",
		client = {
			image = "lawyerpass.png",
		}
	},

	["tirerepairkit"] = {
		label = "Tire Repair Kit",
		weight = 1000,
		stack = true,
		close = true,
		description = "A kit to repair your tires",
		client = {
			image = "tirerepairkit.png",
		}
	},

	["water_bottle"] = {
		label = "Bottle of Water",
		weight = 500,
		stack = true,
		close = true,
		description = "For all the thirsty out there",
		client = {
			image = "water_bottle.png",
		}
	},

	["weed_skunk_seed"] = {
		label = "Skunk Seed",
		weight = 0,
		stack = true,
		close = true,
		description = "A weed seed of Skunk",
		client = {
			image = "weed_seed.png",
		}
	},

	["largescope_attachment"] = {
		label = "Large Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A large scope for a weapon",
		client = {
			image = "largescope_attachment.png",
		}
	},

	["grape"] = {
		label = "Grape",
		weight = 100,
		stack = true,
		close = false,
		description = "Mmmmh yummie, grapes",
		client = {
			image = "grape.png",
		}
	},

	["harness"] = {
		label = "Race Harness",
		weight = 1000,
		stack = false,
		close = true,
		description = "Racing Harness so no matter what you stay in the car",
		client = {
			image = "harness.png",
		}
	},

	["snikkel_candy"] = {
		label = "Snikkel",
		weight = 100,
		stack = true,
		close = true,
		description = "Some delicious candy :O",
		client = {
			image = "snikkel_candy.png",
		}
	},

	["skullcamo_attachment"] = {
		label = "Skull Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A skull camo for a weapon",
		client = {
			image = "skullcamo_attachment.png",
		}
	},

	["bellend_muzzle_brake"] = {
		label = "Bellend Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "bellend_muzzle_brake.png",
		}
	},

	["weed_whitewidow"] = {
		label = "White Widow 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g White Widow",
		client = {
			image = "weed_baggy.png",
		}
	},

	["1060gpu"] = {
		label = "GTX 1060",
		weight = 0,
		stack = false,
		close = true,
		description = "A good upgrade if you are broke.",
		client = {
			image = "1060.png",
		}
	},

	["veh_toolbox"] = {
		label = "Toolbox",
		weight = 1000,
		stack = true,
		close = true,
		description = "Check vehicle status",
		client = {
			image = "veh_toolbox.png",
		}
	},

	["luxuryfinish_attachment"] = {
		label = "Luxury Finish",
		weight = 1000,
		stack = true,
		close = true,
		description = "A luxury finish for a weapon",
		client = {
			image = "luxuryfinish_attachment.png",
		}
	},

	["zebracamo_attachment"] = {
		label = "Zebra Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A zebra camo for a weapon",
		client = {
			image = "zebracamo_attachment.png",
		}
	},

	["filled_evidence_bag"] = {
		label = "Evidence Bag",
		weight = 200,
		stack = false,
		close = false,
		description = "A filled evidence bag to see who committed the crime >:(",
		client = {
			image = "evidence.png",
		}
	},

	["labkey"] = {
		label = "Key",
		weight = 500,
		stack = false,
		close = true,
		description = "Key for a lock...?",
		client = {
			image = "labkey.png",
		}
	},

	["weed_ak47"] = {
		label = "AK47 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g AK47",
		client = {
			image = "weed_baggy.png",
		}
	},

	["gatecrack"] = {
		label = "Gatecrack",
		weight = 0,
		stack = true,
		close = true,
		description = "Handy software to tear down some fences",
		client = {
			image = "usb_device.png",
		}
	},

	["electronickit"] = {
		label = "Electronic Kit",
		weight = 100,
		stack = true,
		close = true,
		description = "If you've always wanted to build a robot you can maybe start here. Maybe you'll be the new Elon Musk?",
		client = {
			image = "electronickit.png",
		}
	},

	["firstaid"] = {
		label = "First Aid",
		weight = 2500,
		stack = true,
		close = true,
		description = "You can use this First Aid kit to get people back on their feet",
		client = {
			image = "firstaid.png",
		}
	},

	["precision_muzzle_brake"] = {
		label = "Precision Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "precision_muzzle_brake.png",
		}
	},

	["1050gpu"] = {
		label = "GTX 1050",
		weight = 0,
		stack = false,
		close = true,
		description = "It does the job for its price.",
		client = {
			image = "1050.png",
		}
	},

	["oxy"] = {
		label = "Prescription Oxy",
		weight = 0,
		stack = true,
		close = true,
		description = "The Label Has Been Ripped Off",
		client = {
			image = "oxy.png",
		}
	},

	["weed_nutrition"] = {
		label = "Plant Fertilizer",
		weight = 2000,
		stack = true,
		close = true,
		description = "Plant nutrition",
		client = {
			image = "weed_nutrition.png",
		}
	},

	["tosti"] = {
		label = "Grilled Cheese Sandwich",
		weight = 200,
		stack = true,
		close = true,
		description = "Nice to eat",
		client = {
			image = "tosti.png",
		}
	},

	["suppressor_attachment"] = {
		label = "Suppressor",
		weight = 1000,
		stack = true,
		close = true,
		description = "A suppressor for a weapon",
		client = {
			image = "suppressor_attachment.png",
		}
	},

	["plastic"] = {
		label = "Plastic",
		weight = 100,
		stack = true,
		close = false,
		description = "RECYCLE! - Greta Thunberg 2019",
		client = {
			image = "plastic.png",
		}
	},

	["markedbills"] = {
		label = "Marked Money",
		weight = 1000,
		stack = false,
		close = true,
		description = "Money?",
		client = {
			image = "markedbills.png",
		}
	},

	["stickynote"] = {
		label = "Sticky note",
		weight = 0,
		stack = false,
		close = false,
		description = "Sometimes handy to remember something :)",
		client = {
			image = "stickynote.png",
		}
	},

	["cleaningkit"] = {
		label = "Cleaning Kit",
		weight = 250,
		stack = true,
		close = true,
		description = "A microfiber cloth with some soap will let your car sparkle again!",
		client = {
			image = "cleaningkit.png",
		}
	},

	["veh_plates"] = {
		label = "Plates",
		weight = 1000,
		stack = true,
		close = true,
		description = "Install vehicle plates",
		client = {
			image = "veh_plates.png",
		}
	},

	["casinochips"] = {
		label = "Casino Chips",
		weight = 0,
		stack = true,
		close = false,
		description = "Chips For Casino Gambling",
		client = {
			image = "casinochips.png",
		}
	},

	["samsungphone"] = {
		label = "Samsung S10",
		weight = 1000,
		stack = true,
		close = true,
		description = "Very expensive phone",
		client = {
			image = "samsungphone.png",
		}
	},

	["barrel_attachment"] = {
		label = "Barrel",
		weight = 1000,
		stack = true,
		close = true,
		description = "A barrel for a weapon",
		client = {
			image = "barrel_attachment.png",
		}
	},

	["weed_amnesia"] = {
		label = "Amnesia 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g Amnesia",
		client = {
			image = "weed_baggy.png",
		}
	},

	["flashlight_attachment"] = {
		label = "Flashlight",
		weight = 1000,
		stack = true,
		close = true,
		description = "A flashlight for a weapon",
		client = {
			image = "flashlight_attachment.png",
		}
	},

	["antipatharia_coral"] = {
		label = "Antipatharia",
		weight = 1000,
		stack = true,
		close = true,
		description = "Its also known as black corals or thorn corals",
		client = {
			image = "antipatharia_coral.png",
		}
	},

	["iron"] = {
		label = "Iron",
		weight = 100,
		stack = true,
		close = false,
		description = "Handy piece of metal that you can probably use for something",
		client = {
			image = "iron.png",
		}
	},

	["rubber"] = {
		label = "Rubber",
		weight = 100,
		stack = true,
		close = false,
		description = "Rubber, I believe you can make your own rubber ducky with it :D",
		client = {
			image = "rubber.png",
		}
	},

	["sessantacamo_attachment"] = {
		label = "Sessanta Nove Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A sessanta nove camo for a weapon",
		client = {
			image = "sessantacamo_attachment.png",
		}
	},

	["tenkgoldchain"] = {
		label = "10k Gold Chain",
		weight = 200,
		stack = true,
		close = true,
		description = "10 carat golden chain",
		client = {
			image = "10kgoldchain.png",
		}
	},

	["advancedrepairkit"] = {
		label = "Advanced Repairkit",
		weight = 4000,
		stack = true,
		close = true,
		description = "A nice toolbox with stuff to repair your vehicle",
		client = {
			image = "advancedkit.png",
		}
	},

	["twerks_candy"] = {
		label = "Twerks",
		weight = 100,
		stack = true,
		close = true,
		description = "Some delicious candy :O",
		client = {
			image = "twerks_candy.png",
		}
	},

	["comp_attachment"] = {
		label = "Compensator",
		weight = 1000,
		stack = true,
		close = true,
		description = "A compensator for a weapon",
		client = {
			image = "comp_attachment.png",
		}
	},

	["ifaks"] = {
		label = "ifaks",
		weight = 200,
		stack = true,
		close = true,
		description = "ifaks for healing and a complete stress remover.",
		client = {
			image = "ifaks.png",
		}
	},

	["empty_weed_bag"] = {
		label = "Empty Weed Bag",
		weight = 0,
		stack = true,
		close = true,
		description = "A small empty bag",
		client = {
			image = "weed_baggy_empty.png",
		}
	},

	["diamond_ring"] = {
		label = "Diamond Ring",
		weight = 200,
		stack = true,
		close = true,
		description = "A diamond ring seems like the jackpot to me!",
		client = {
			image = "diamond_ring.png",
		}
	},

	["thermalpast"] = {
		label = "Thermal past",
		weight = 0,
		stack = false,
		close = true,
		description = "A thermalpast To keep your cpu chiling.",
		client = {
			image = "thermalpast.png",
		}
	},

	["pinger"] = {
		label = "Pinger",
		weight = 1000,
		stack = true,
		close = true,
		description = "With a pinger and your phone you can send out your location",
		client = {
			image = "pinger.png",
		}
	},

	["firework4"] = {
		label = "Weeping Willow",
		weight = 1000,
		stack = true,
		close = true,
		description = "Fireworks",
		client = {
			image = "firework4.png",
		}
	},

	["veh_neons"] = {
		label = "Neons",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle neons",
		client = {
			image = "veh_neons.png",
		}
	},

	["metalscrap"] = {
		label = "Metal Scrap",
		weight = 100,
		stack = true,
		close = false,
		description = "You can probably make something nice out of this",
		client = {
			image = "metalscrap.png",
		}
	},

	["certificate"] = {
		label = "Certificate",
		weight = 0,
		stack = true,
		close = true,
		description = "Certificate that proves you own certain stuff",
		client = {
			image = "certificate.png",
		}
	},

	["weed_purplehaze"] = {
		label = "Purple Haze 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g Purple Haze",
		client = {
			image = "weed_baggy.png",
		}
	},


    ["driver_license"] = {
		label = "Drivers License",
		weight = 0,
		stack = false,
		close = false,
		description = "Permit to show you can drive a vehicle",
	},



	["leopardcamo_attachment"] = {
		label = "Leopard Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A leopard camo for a weapon",
		client = {
			image = "leopardcamo_attachment.png",
		}
	},

	["perseuscamo_attachment"] = {
		label = "Perseus Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A perseus camo for a weapon",
		client = {
			image = "perseuscamo_attachment.png",
		}
	},

	["goldchain"] = {
		label = "Golden Chain",
		weight = 150,
		stack = true,
		close = true,
		description = "A golden chain seems like the jackpot to me!",
		client = {
			image = "goldchain.png",
		}
	},

	["weed_ogkush"] = {
		label = "OGKush 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g OG Kush",
		client = {
			image = "weed_baggy.png",
		}
	},

	["flat_muzzle_brake"] = {
		label = "Flat Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "flat_muzzle_brake.png",
		}
	},

	["trojan_usb"] = {
		label = "Trojan USB",
		weight = 0,
		stack = true,
		close = true,
		description = "Handy software to shut down some systems",
		client = {
			image = "usb_device.png",
		}
	},

	["binoculars"] = {
		label = "Binoculars",
		weight = 600,
		stack = true,
		close = true,
		description = "Sneaky Breaky...",
		client = {
			image = "binoculars.png",
		}
	},

	["bank_card"] = {
		label = "Bank Card",
		weight = 0,
		stack = false,
		close = true,
		description = "Used to access ATM",
		client = {
			image = "bank_card.png",
		}
	},

	["advscope_attachment"] = {
		label = "Advanced Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "An advanced scope for a weapon",
		client = {
			image = "advscope_attachment.png",
		}
	},

	["split_end_muzzle_brake"] = {
		label = "Split End Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "split_end_muzzle_brake.png",
		}
	},

	["grip_attachment"] = {
		label = "Grip",
		weight = 1000,
		stack = true,
		close = true,
		description = "A grip for a weapon",
		client = {
			image = "grip_attachment.png",
		}
	},

	["kurkakola"] = {
		label = "Cola",
		weight = 500,
		stack = true,
		close = true,
		description = "For all the thirsty out there",
		client = {
			image = "cola.png",
		}
	},

	["id_card"] = {
		label = "ID Card",
		weight = 0,
		stack = false,
		close = false,
		description = "A card containing all your information to identify yourself",
	},

	["woodcamo_attachment"] = {
		label = "Woodland Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A woodland camo for a weapon",
		client = {
			image = "woodcamo_attachment.png",
		}
	},

	["newscam"] = {
		label = "News Camera",
		weight = 100,
		stack = false,
		close = true,
		description = "A camera for the news",
		client = {
			image = "newscam.png",
		}
	},

	["3060gpu"] = {
		label = "RTX 3060",
		weight = 0,
		stack = false,
		close = true,
		description = "IDK Man! It's so expensive.",
		client = {
			image = "3060.png",
		}
	},

	["steel"] = {
		label = "Steel",
		weight = 100,
		stack = true,
		close = false,
		description = "Nice piece of metal that you can probably use for something",
		client = {
			image = "steel.png",
		}
	},

	["coke_brick"] = {
		label = "Coke Brick",
		weight = 1000,
		stack = false,
		close = true,
		description = "Heavy package of cocaine, mostly used for deals and takes a lot of space",
		client = {
			image = "coke_brick.png",
		}
	},

	["weed_ogkush_seed"] = {
		label = "OGKush Seed",
		weight = 0,
		stack = true,
		close = true,
		description = "A weed seed of OG Kush",
		client = {
			image = "weed_seed.png",
		}
	},

	["veh_armor"] = {
		label = "Armor",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle armor",
		client = {
			image = "veh_armor.png",
		}
	},

	["boombox"] = {
		label = "Boom Box",
		weight = 100,
		stack = false,
		close = true,
		description = "A Useable Boombox",
		client = {
			image = "boombox.png",
		}
	},

	["weed_skunk"] = {
		label = "Skunk 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g Skunk",
		client = {
			image = "weed_baggy.png",
		}
	},

	["diamond"] = {
		label = "Diamond",
		weight = 1000,
		stack = true,
		close = true,
		description = "A diamond seems like the jackpot to me!",
		client = {
			image = "diamond.png",
		}
	},

	["cryptostick"] = {
		label = "Crypto Stick",
		weight = 200,
		stack = false,
		close = true,
		description = "Why would someone ever buy money that doesn't exist.. How many would it contain..?",
		client = {
			image = "cryptostick.png",
		}
	},

	["squared_muzzle_brake"] = {
		label = "Squared Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "squared_muzzle_brake.png",
		}
	},

	["veh_brakes"] = {
		label = "Brakes",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle brakes",
		client = {
			image = "veh_brakes.png",
		}
	},

	["screwdriverset"] = {
		label = "Toolkit",
		weight = 1000,
		stack = true,
		close = false,
		description = "Very useful to screw... screws...",
		client = {
			image = "screwdriverset.png",
		}
	},

	["drum_attachment"] = {
		label = "Drum",
		weight = 1000,
		stack = true,
		close = true,
		description = "A drum for a weapon",
		client = {
			image = "drum_attachment.png",
		}
	},

	["coffee"] = {
		label = "Coffee",
		weight = 200,
		stack = true,
		close = true,
		description = "Pump 4 Caffeine",
		client = {
			image = "coffee.png",
		}
	},

	["firework2"] = {
		label = "Poppelers",
		weight = 1000,
		stack = true,
		close = true,
		description = "Fireworks",
		client = {
			image = "firework2.png",
		}
	},

	["veh_engine"] = {
		label = "Engine",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle engine",
		client = {
			image = "veh_engine.png",
		}
	},

	["grapejuice"] = {
		label = "Grape Juice",
		weight = 200,
		stack = true,
		close = false,
		description = "Grape juice is said to be healthy",
		client = {
			image = "grapejuice.png",
		}
	},

	["veh_transmission"] = {
		label = "Transmission",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle transmission",
		client = {
			image = "veh_transmission.png",
		}
	},

	["medscope_attachment"] = {
		label = "Medium Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A medium scope for a weapon",
		client = {
			image = "medscope_attachment.png",
		}
	},

	["dendrogyra_coral"] = {
		label = "Dendrogyra",
		weight = 1000,
		stack = true,
		close = true,
		description = "Its also known as pillar coral",
		client = {
			image = "dendrogyra_coral.png",
		}
	},

	["clothing_bag"] = {
		label = "Clothing Bag",
		weight = 100,
		stack = true,
		close = true,
		description = "",
		client = {
			image = "clothing_bag.png",
		}
	},

	["newsmic"] = {
		label = "News Microphone",
		weight = 100,
		stack = false,
		close = true,
		description = "A microphone for the news",
		client = {
			image = "newsmic.png",
		}
	},

	["police_stormram"] = {
		label = "Stormram",
		weight = 18000,
		stack = true,
		close = true,
		description = "A nice tool to break into doors",
		client = {
			image = "police_stormram.png",
		}
	},

	["iphone"] = {
		label = "iPhone",
		weight = 1000,
		stack = true,
		close = true,
		description = "Very expensive phone",
		client = {
			image = "iphone.png",
		}
	},

	["uncut_emerald"] = {
		label = "Uncut Emerald",
		weight = 100,
		stack = true,
		close = false,
		description = "A rough Emerald",
		client = {
			image = "uncut_emerald.png",
		}
	},

	["mininglaser"] = {
		label = "Mining Laser",
		weight = 900,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "mininglaser.png",
		}
	},

	["emerald_ring"] = {
		label = "Emerald Ring",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "emerald_ring.png",
		}
	},

	["pickaxe"] = {
		label = "Pickaxe",
		weight = 1000,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "pickaxe.png",
		}
	},

	["ruby_earring"] = {
		label = "Ruby Earrings",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "ruby_earring.png",
		}
	},

	["bottle"] = {
		label = "Empty Bottle",
		weight = 10,
		stack = true,
		close = false,
		description = "A glass bottle",
		client = {
			image = "bottle.png",
		}
	},

	["sapphire_ring_silver"] = {
		label = "Sapphire Ring Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "sapphire_ring_silver.png",
		}
	},

	["sapphire"] = {
		label = "Sapphire",
		weight = 100,
		stack = true,
		close = false,
		description = "A Sapphire that shimmers",
		client = {
			image = "sapphire.png",
		}
	},

	["uncut_diamond"] = {
		label = "Uncut Diamond",
		weight = 100,
		stack = true,
		close = false,
		description = "A rough Diamond",
		client = {
			image = "uncut_diamond.png",
		}
	},

	["emerald_earring"] = {
		label = "Emerald Earrings",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "emerald_earring.png",
		}
	},

	["ironore"] = {
		label = "Iron Ore",
		weight = 1000,
		stack = true,
		close = false,
		description = "Iron, a base ore.",
		client = {
			image = "ironore.png",
		}
	},

	["diamond_earring"] = {
		label = "Diamond Earrings",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "diamond_earring.png",
		}
	},

	["sapphire_ring"] = {
		label = "Sapphire Ring",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "sapphire_ring.png",
		}
	},

	["can"] = {
		label = "Empty Can",
		weight = 10,
		stack = true,
		close = false,
		description = "An empty can, good for recycling",
		client = {
			image = "can.png",
		}
	},

	["diamond_necklace_silver"] = {
		label = "Diamond Necklace Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "diamond_necklace_silver.png",
		}
	},

	["ruby_earring_silver"] = {
		label = "Ruby Earrings Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "ruby_earring_silver.png",
		}
	},

	["uncut_sapphire"] = {
		label = "Uncut Sapphire",
		weight = 100,
		stack = true,
		close = false,
		description = "A rough Sapphire",
		client = {
			image = "uncut_sapphire.png",
		}
	},

	["diamond_necklace"] = {
		label = "Diamond Necklace",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "diamond_necklace.png",
		}
	},

	["sapphire_necklace"] = {
		label = "Sapphire Necklace",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "sapphire_necklace.png",
		}
	},

	["emerald_necklace"] = {
		label = "Emerald Necklace",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "emerald_necklace.png",
		}
	},

	["emerald_necklace_silver"] = {
		label = "Emerald Necklace Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "emerald_necklace_silver.png",
		}
	},

	["sapphire_earring"] = {
		label = "Sapphire Earrings",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "sapphire_earring.png",
		}
	},

	["carbon"] = {
		label = "Carbon",
		weight = 1000,
		stack = true,
		close = false,
		description = "Carbon, a base ore.",
		client = {
			image = "carbon.png",
		}
	},

	["silverore"] = {
		label = "Silver Ore",
		weight = 1000,
		stack = true,
		close = false,
		description = "Silver Ore",
		client = {
			image = "silverore.png",
		}
	},

	["sapphire_earring_silver"] = {
		label = "Sapphire Earrings Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "sapphire_earring_silver.png",
		}
	},

	["miningdrill"] = {
		label = "Mining Drill",
		weight = 1000,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "miningdrill.png",
		}
	},

	["ruby"] = {
		label = "Ruby",
		weight = 100,
		stack = true,
		close = false,
		description = "A Ruby that shimmers",
		client = {
			image = "ruby.png",
		}
	},

	["drillbit"] = {
		label = "Drill Bit",
		weight = 10,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "drillbit.png",
		}
	},

	["goldore"] = {
		label = "Gold Ore",
		weight = 1000,
		stack = true,
		close = false,
		description = "Gold Ore",
		client = {
			image = "goldore.png",
		}
	},

	["silver_ring"] = {
		label = "Silver Ring",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "silver_ring.png",
		}
	},

	["ruby_necklace"] = {
		label = "Ruby Necklace",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "ruby_necklace.png",
		}
	},

	["silverchain"] = {
		label = "Silver Chain",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "silverchain.png",
		}
	},

	["emerald_ring_silver"] = {
		label = "Emerald Ring Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "emerald_ring_silver.png",
		}
	},

	["goldingot"] = {
		label = "Gold Ingot",
		weight = 1000,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "goldingot.png",
		}
	},

	["silveringot"] = {
		label = "Silver Ingot",
		weight = 1000,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "silveringot.png",
		}
	},

	["stone"] = {
		label = "Stone",
		weight = 1000,
		stack = true,
		close = false,
		description = "Stone woo",
		client = {
			image = "stone.png",
		}
	},

	["diamond_ring_silver"] = {
		label = "Diamond Ring Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "diamond_ring_silver.png",
		}
	},

	["emerald_earring_silver"] = {
		label = "Emerald Earrings Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "emerald_earring_silver.png",
		}
	},

	["diamond_earring_silver"] = {
		label = "Diamond Earrings Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "diamond_earring_silver.png",
		}
	},

	["goldpan"] = {
		label = "Gold Panning Tray",
		weight = 10,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "goldpan.png",
		}
	},

	["emerald"] = {
		label = "Emerald",
		weight = 100,
		stack = true,
		close = false,
		description = "A Emerald that shimmers",
		client = {
			image = "emerald.png",
		}
	},

	["gold_ring"] = {
		label = "Gold Ring",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "gold_ring.png",
		}
	},

	["copperore"] = {
		label = "Copper Ore",
		weight = 1000,
		stack = true,
		close = false,
		description = "Copper, a base ore.",
		client = {
			image = "copperore.png",
		}
	},

	["goldearring"] = {
		label = "Golden Earrings",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "gold_earring.png",
		}
	},

	["ruby_necklace_silver"] = {
		label = "Ruby Necklace Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "ruby_necklace_silver.png",
		}
	},

	["uncut_ruby"] = {
		label = "Uncut Ruby",
		weight = 100,
		stack = true,
		close = false,
		description = "A rough Ruby",
		client = {
			image = "uncut_ruby.png",
		}
	},

	["silverearring"] = {
		label = "Silver Earrings",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "silver_earring.png",
		}
	},

	["ruby_ring"] = {
		label = "Ruby Ring",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "ruby_ring.png",
		}
	},

	["sapphire_necklace_silver"] = {
		label = "Sapphire Necklace Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "sapphire_necklace_silver.png",
		}
	},

	["ruby_ring_silver"] = {
		label = "Ruby Ring Silver",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "ruby_ring_silver.png",
		}
	},

	["alcoholtester"] = {
		label = "Alcohol Tester",
		weight = 400,
		stack = false,
		close = true,
		description = "For testing purposes..",
		client = {
			image = "alcoholtester.png",
		}
	},

	["leo_gps"] = {
		label = "LEO GPS",
		weight = 200,
		stack = false,
		close = true,
		description = "Show your gps location to others",
		client = {
			image = "leo-gps.png",
		}
	},

	["cuffkeys"] = {
		label = "Cuff Keys",
		weight = 75,
		stack = true,
		close = true,
		description = "Set them free !",
		client = {
			image = "cuffkeys.png",
		}
	},

	["broken_handcuffs"] = {
		label = "Broken Handcuffs",
		weight = 100,
		stack = true,
		close = true,
		description = "It's broken, maybe you can repair it?",
		client = {
			image = "broken-handcuffs.png",
		}
	},

	["bolt_cutter"] = {
		label = "Bolt Cutter",
		weight = 50,
		stack = true,
		close = true,
		description = "Wanna cut some metal items ?",
		client = {
			image = "bolt_cutter.png",
		}
	},

	["ziptie"] = {
		label = "ZipTie",
		weight = 50,
		stack = true,
		close = true,
		description = "Comes in handy when people misbehave. Maybe it can be used for something else?",
		client = {
			image = "ziptie.png",
		}
	},

	["flush_cutter"] = {
		label = "Flush Cutter",
		weight = 50,
		stack = true,
		close = true,
		description = "Comes in handy when you want to cut zipties..",
		client = {
			image = "flush_cutter.png",
		}
	},

	["insurance"] = {
		label = "Insurance Card",
		weight = 100,
		stack = false,
		close = true,
		description = "Insurance card",
		client = {
			image = "insurance.png",
		}
	},

	["bobbypins"] = {
		label = "Bobbypins",
		weight = 50,
		stack = true,
		close = true,
		description = "An box of bobbypins",
		client = {
			image = "bobbypins.png",
		}
	},

	["pliers"] = {
		label = "Pliers",
		weight = 50,
		stack = true,
		close = true,
		description = "A ordinary pair of the pliers",
		client = {
			image = "pliers.png",
		}
	},

	["rc-ruiner"] = {
		label = "RC Ruiner",
		weight = 200,
		stack = true,
		close = true,
		description = "A remote controlled car",
		client = {
			image = "rcruiner.png",
		}
	},
	-- Place in ox_inventory/data/items.lua
	['tuna'] = {
		label = 'Tuna',
		weight = 65,
		stack = true,
		close = false,
	},
	
	['salmon'] = {
		label = 'Salmon',
		weight = 50,
		stack = true,
		close = false,
	},

	['trout'] = {
		label = 'Trout',
		weight = 50,
		stack = true,
		close = false,
	},

	['anchovy'] = {
		label = 'Anchovy',
		weight = 50,
		stack = true,
		close = false,
	},

	['fishbait'] = {
		label = 'Fish Bait',
		weight = 50,
		stack = true,
		close = false,
	},

	['fishingrod'] = {
		label = 'Fishing Rod',
		weight = 800,
		stack = true,
		close = true,
	},

	["renting_contract"] = {
		label = "Renting Contract",
		weight = 500,
		stack = false,
		close = true,
		description = "",
		client = {
			image = "renting_contract.png",
		}
	},

	["sturgeon"] = {
		label = "Sturgeon",
		weight = 70,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "sturgeon.png",
		}
	},

	["herring"] = {
		label = "Herring",
		weight = 75,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "herring.png",
		}
	},

	["barracuda"] = {
		label = "Barracuda",
		weight = 80,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "barracuda.png",
		}
	},

	["bluefish"] = {
		label = "Bluefish",
		weight = 65,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "bluefish.png",
		}
	},

	["grouper"] = {
		label = "Grouper",
		weight = 65,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "grouper.png",
		}
	},

	["haddock"] = {
		label = "Haddock",
		weight = 55,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "haddock.png",
		}
	},

	["bass"] = {
		label = "Bass",
		weight = 55,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "bass.png",
		}
	},

	["catfish"] = {
		label = "Catfish",
		weight = 65,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "catfish.png",
		}
	},

	["perch"] = {
		label = "Perch",
		weight = 55,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "perch.png",
		}
	},

	["halibut"] = {
		label = "Halibut",
		weight = 65,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "halibut.png",
		}
	},

	["marlin"] = {
		label = "Marlin",
		weight = 85,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "marlin.png",
		}
	},

	["mackerel"] = {
		label = "Mackerel",
		weight = 75,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "mackerel.png",
		}
	},

	["sardine"] = {
		label = "Sardine",
		weight = 45,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "sardine.png",
		}
	},

	["bream"] = {
		label = "Bream",
		weight = 55,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "bream.png",
		}
	},

	["tilapia"] = {
		label = "Tilapia",
		weight = 55,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "tilapia.png",
		}
	},

	["eel"] = {
		label = "Eel",
		weight = 65,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "eel.png",
		}
	},

	["swordfish"] = {
		label = "Swordfish",
		weight = 105,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "swordfish.png",
		}
	},

	["cod"] = {
		label = "Cod",
		weight = 55,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "cod.png",
		}
	},

	["pike"] = {
		label = "Pike",
		weight = 95,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "pike.png",
		}
	},

	["flounder"] = {
		label = "Flounder",
		weight = 55,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "flounder.png",
		}
	},

	["snapper"] = {
		label = "Snapper",
		weight = 65,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "snapper.png",
		}
	},

	["char"] = {
		label = "Char",
		weight = 75,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "char.png",
		}
	},

	["carp"] = {
		label = "Carp",
		weight = 65,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "carp.png",
		}
	},

	["shad"] = {
		label = "Shad",
		weight = 55,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "shad.png",
		}
	},

	["pollock"] = {
		label = "Atlantic Pollock",
		weight = 50,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "pollock.png",
		}
	},

	["redfish"] = {
		label = "Acadian Redfish",
		weight = 105,
		stack = true,
		close = true,
		description = "A breed of fish.",
		client = {
			image = "redfish.png",
		}
	},

	["methlab"] = {
		label = "Lab",
		weight = 1500,
		stack = true,
		close = false,
		description = "A portable Meth Lab",
		client = {
			image = "lab.png",
		}
	},

	["lithium"] = {
		label = "Dead Batteries",
		weight = 100,
		stack = true,
		close = false,
		description = "Lithium, something you can make Meth with!",
		client = {
			image = "deadbatteries.png",
		}
	},

	["acetone"] = {
		label = "Acetone",
		weight = 500,
		stack = true,
		close = false,
		description = "It is a colourless, highly volatile and flammable liquid with a characteristic pungent odour.",
		client = {
			image = "acetone.png",
		}
	},

	["wires"] = {
		label = "Electrical Wires",
		weight = 50,
		stack = true,
		close = true,
		description = "Torn Electrical Wires",
		client = {
			image = "wires.png",
		}
	},

	["roach"] = {
		label = "Cockroach",
		weight = 50,
		stack = true,
		close = true,
		description = "A live cockroach",
		client = {
			image = "roach.png",
		}
	},

	["rat"] = {
		label = "Dead Rat",
		weight = 50,
		stack = true,
		close = true,
		description = "Stinky dead rat",
		client = {
			image = "rat.png",
		}
	},

	["newspaper"] = {
		label = "Old Newspaper",
		weight = 50,
		stack = true,
		close = true,
		description = "Last weeks newspaper",
		client = {
			image = "newspaper.png",
		}
	},

	["napkin"] = {
		label = "Greasy Napkin",
		weight = 50,
		stack = true,
		close = true,
		description = "Napkin with leftover ketchop",
		client = {
			image = "napkin.png",
		}
	},

	["cardboard"] = {
		label = "Ripped Cardboard",
		weight = 50,
		stack = true,
		close = true,
		description = "Ripped Cardboard",
		client = {
			image = "cardboard.png",
		}
	},

	["scrapwood"] = {
		label = "Broken Peice of Wood",
		weight = 50,
		stack = true,
		close = true,
		description = "Karate chopped wood",
		client = {
			image = "scrapwood.png",
		}
	},

	["safetypin"] = {
		label = "Broken Safety Pin",
		weight = 50,
		stack = true,
		close = true,
		description = "Broken Safety Pin",
		client = {
			image = "safetypin.png",
		}
	},

	["cd"] = {
		label = "Compact Disk",
		weight = 50,
		stack = true,
		close = true,
		description = "Relic of the 90s",
		client = {
			image = "cd.png",
		}
	},

	["gum"] = {
		label = "Empty Pack of Gum",
		weight = 50,
		stack = true,
		close = true,
		description = "Its empty",
		client = {
			image = "gum.png",
		}
	},

	["wrapper"] = {
		label = "Used Food Wrapper",
		weight = 50,
		stack = true,
		close = true,
		description = "Up-N-Atom Burger wrapper",
		client = {
			image = "wrapper.png",
		}
	},

	["ducttape"] = {
		label = "Duct Tape",
		weight = 50,
		stack = true,
		close = true,
		description = "Roll of Duct Tape",
		client = {
			image = "ducttape.png",
		}
	},

	["sudafed"] = {
		label = "Sudafed",
		weight = 1000,
		stack = true,
		close = false,
		description = "Sudafed, Powerful nasal decongestant with pseudoephedrine for severe relief.",
		client = {
			image = "sudafed.png",
		}
	},

	["nfsmw"] = {
		label = "Need for Speed: Most Wanted",
		weight = 50,
		stack = true,
		close = true,
		description = "Need for Speed: Most Wanted Game",
		client = {
			image = "nfsmw.png",
		}
	},

	["piratelego"] = {
		label = "Pirate Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Pirate Lego Figure",
		client = {
			image = "piratelego.png",
		}
	},

	["tombraider"] = {
		label = "Tomb Raider",
		weight = 50,
		stack = true,
		close = true,
		description = "Tomb Raider Game",
		client = {
			image = "tombraider.png",
		}
	},

	["ironmanlego"] = {
		label = "Ironman Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Ironman Lego Figure",
		client = {
			image = "ironmanlego.png",
		}
	},

	["mushi"] = {
		label = "Mushi Figure",
		weight = 30,
		stack = true,
		close = true,
		description = "A Mushi Figure",
		client = {
			image = "mushi.png",
		}
	},

	["silverninjalego"] = {
		label = "Silver Ninja Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Silver Ninja Lego Figure",
		client = {
			image = "silverninjalego.png",
		}
	},

	["midnightclub3"] = {
		label = "Midnight Club 3",
		weight = 50,
		stack = true,
		close = true,
		description = "Midnight Club 3 Game",
		client = {
			image = "midnightclub3.png",
		}
	},

	["amp2"] = {
		label = "Bass Amp",
		weight = 200,
		stack = true,
		close = true,
		description = "Amplifier 2",
		client = {
			image = "amp2.png",
		}
	},

	["diablo"] = {
		label = "Diablo",
		weight = 50,
		stack = true,
		close = true,
		description = "Diablo Game",
		client = {
			image = "diablo.png",
		}
	},

	["plamsatv"] = {
		label = "Plasma TV",
		weight = 1400,
		stack = true,
		close = true,
		description = "Plasma Television",
		client = {
			image = "plamsatv.png",
		}
	},

	["reeltoreel"] = {
		label = "Reel-to-Reel Tape Recorder",
		weight = 1200,
		stack = true,
		close = true,
		description = "Classic reel-to-reel tape recorder",
		client = {
			image = "reeltoreel.png",
		}
	},

	["nfsug2"] = {
		label = "Need for Speed: Underground 2",
		weight = 50,
		stack = true,
		close = true,
		description = "Need for Speed: Underground 2 Game",
		client = {
			image = "nfsug2.png",
		}
	},

	["mac"] = {
		label = "Mac",
		weight = 100,
		stack = true,
		close = true,
		description = "A Mac Computer",
		client = {
			image = "mac.png",
		}
	},

	["macbook2"] = {
		label = "Macbook 2",
		weight = 80,
		stack = true,
		close = true,
		description = "A Macbook 2",
		client = {
			image = "macbook2.png",
		}
	},

	["ps2"] = {
		label = "PlayStation 2",
		weight = 100,
		stack = true,
		close = true,
		description = "PlayStation 2 Console",
		client = {
			image = "ps2.png",
		}
	},

	["motorstorm"] = {
		label = "MotorStorm",
		weight = 50,
		stack = true,
		close = true,
		description = "MotorStorm Game",
		client = {
			image = "motorstorm.png",
		}
	},

	["fallout"] = {
		label = "Fallout",
		weight = 50,
		stack = true,
		close = true,
		description = "Fallout Game",
		client = {
			image = "fallout.png",
		}
	},

	["supermanlego"] = {
		label = "Superman Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Superman Lego Figure",
		client = {
			image = "supermanlego.png",
		}
	},

	["toycar"] = {
		label = "Toy Car",
		weight = 50,
		stack = true,
		close = true,
		description = "A Toy Car",
		client = {
			image = "toycar.png",
		}
	},

	["blackninjalego2"] = {
		label = "Black Ninja Lego 2",
		weight = 30,
		stack = true,
		close = true,
		description = "Black Ninja Lego Figure 2",
		client = {
			image = "blackninjalego2.png",
		}
	},

	["burnout3"] = {
		label = "Burnout 3",
		weight = 50,
		stack = true,
		close = true,
		description = "Burnout 3 Game",
		client = {
			image = "burnout3.png",
		}
	},

	["ratchandclank"] = {
		label = "Ratchet & Clank",
		weight = 50,
		stack = true,
		close = true,
		description = "Ratchet & Clank Game",
		client = {
			image = "ratchetandclank.png",
		}
	},

	["samarilego"] = {
		label = "Samurai  Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Samari Lego Figure",
		client = {
			image = "samarilego.png",
		}
	},

	["shadowofmordor"] = {
		label = "Shadow of Mordor",
		weight = 50,
		stack = true,
		close = true,
		description = "Shadow of Mordor Game",
		client = {
			image = "shadowofmordor.png",
		}
	},

	["goldcoin"] = {
		label = "Gold Coin",
		weight = 5,
		stack = true,
		close = true,
		description = "A Gold Coin",
		client = {
			image = "goldcoin.png",
		}
	},

	["gtasa"] = {
		label = "Grand Theft Auto: San Andreas",
		weight = 50,
		stack = true,
		close = true,
		description = "Grand Theft Auto: San Andreas Game",
		client = {
			image = "gtasa.png",
		}
	},

	["jaxandaxter"] = {
		label = "Jak and Daxter",
		weight = 50,
		stack = true,
		close = true,
		description = "Jak and Daxter Game",
		client = {
			image = "jaxandaxter.png",
		}
	},

	["ibanez"] = {
		label = "Ibanez Electric Guitar",
		weight = 150,
		stack = true,
		close = true,
		description = "Ibanez Guitar",
		client = {
			image = "ibanez.png",
		}
	},

	["bf3"] = {
		label = "Battlefield 3",
		weight = 50,
		stack = true,
		close = true,
		description = "Battlefield 3 Game",
		client = {
			image = "bf3.png",
		}
	},

	["blkwidowlego"] = {
		label = "Black Widow Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Black Widow Lego Figure",
		client = {
			image = "blkwidowlego.png",
		}
	},

	["tennis"] = {
		label = "Tennis",
		weight = 50,
		stack = true,
		close = true,
		description = "Tennis Game",
		client = {
			image = "tennis.png",
		}
	},

	["mac2"] = {
		label = "Mac 2",
		weight = 100,
		stack = true,
		close = true,
		description = "A Mac Computer 2",
		client = {
			image = "mac2.png",
		}
	},

	["silvercoin"] = {
		label = "Silver Coin",
		weight = 5,
		stack = true,
		close = true,
		description = "A Silver Coin",
		client = {
			image = "silvercoin.png",
		}
	},

	["prisonlego"] = {
		label = "Prison Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Prison Lego Figure",
		client = {
			image = "prisonlego.png",
		}
	},

	["blueninjalego"] = {
		label = "Blue Ninja Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Blue Ninja Lego Figure",
		client = {
			image = "blueninjalego.png",
		}
	},

	["gargoyle"] = {
		label = "Gargoyle",
		weight = 100,
		stack = true,
		close = true,
		description = "A Gargoyle Figure",
		client = {
			image = "gargoyle.png",
		}
	},

	["greenninjalego"] = {
		label = "Green Ninja Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Green Ninja Lego Figure",
		client = {
			image = "greenninjalego.png",
		}
	},

	["ps4"] = {
		label = "PS4 Console",
		weight = 100,
		stack = true,
		close = true,
		description = "A PlayStation 4 Console",
		client = {
			image = "ps4.png",
		}
	},

	["amp"] = {
		label = "Guitar Amp",
		weight = 200,
		stack = true,
		close = true,
		description = "Amplifier",
		client = {
			image = "amp.png",
		}
	},

	["batmanarkham"] = {
		label = "Batman: Arkham",
		weight = 50,
		stack = true,
		close = true,
		description = "Batman: Arkham Game",
		client = {
			image = "batmanarkham.png",
		}
	},

	["blackninjalego"] = {
		label = "Black Ninja Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Black Ninja Lego Figure",
		client = {
			image = "blackninjalego.png",
		}
	},

	["oblivion"] = {
		label = "The Elder Scrolls IV: Oblivion",
		weight = 50,
		stack = true,
		close = true,
		description = "The Elder Scrolls IV: Oblivion Game",
		client = {
			image = "oblivion.png",
		}
	},

	["urn"] = {
		label = "Urn",
		weight = 60,
		stack = true,
		close = true,
		description = "An Urn",
		client = {
			image = "urn.png",
		}
	},

	["batman"] = {
		label = "Batman",
		weight = 50,
		stack = true,
		close = true,
		description = "Batman Game",
		client = {
			image = "batman.png",
		}
	},

	["silenthill"] = {
		label = "Silent Hill",
		weight = 50,
		stack = true,
		close = true,
		description = "Silent Hill Game",
		client = {
			image = "silenthill.png",
		}
	},

	["denonamp"] = {
		label = "Denon Amplifier",
		weight = 800,
		stack = true,
		close = true,
		description = "Denon audio amplifier",
		client = {
			image = "denonamp.png",
		}
	},

	["sandmanlego"] = {
		label = "Sandman Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Sandman Lego Figure",
		client = {
			image = "sandmanlego.png",
		}
	},

	["gargoyles2"] = {
		label = "Gargoyles 2",
		weight = 50,
		stack = true,
		close = true,
		description = "Gargoyles 2 Game",
		client = {
			image = "gargoyles2.png",
		}
	},

	["hd600"] = {
		label = "Sennheiser HD 600",
		weight = 200,
		stack = true,
		close = true,
		description = "Sennheiser HD 600 Headphones",
		client = {
			image = "hd600.png",
		}
	},

	["hoodie"] = {
		label = "Hoodie",
		weight = 20,
		stack = true,
		close = true,
		description = "A Hoodie",
		client = {
			image = "hoodie.png",
		}
	},

	["btheadphones"] = {
		label = "Bluetooth Headphones",
		weight = 50,
		stack = true,
		close = true,
		description = "Bluetooth Headphones",
		client = {
			image = "btheadphones.png",
		}
	},

	["caplego"] = {
		label = "Captain America Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Captain America Lego Figure",
		client = {
			image = "caplego.png",
		}
	},

	["gps"] = {
		label = "GPS",
		weight = 30,
		stack = true,
		close = true,
		description = "A GPS Device",
		client = {
			image = "gps.png",
		}
	},

	["goldcup"] = {
		label = "Gold Cup",
		weight = 10,
		stack = true,
		close = true,
		description = "A Gold Cup",
		client = {
			image = "goldcup.png",
		}
	},

	["apemanlego"] = {
		label = "Ape-Man Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Ape-Man Lego Figure",
		client = {
			image = "apemanlego.png",
		}
	},

	["gow2"] = {
		label = "God of War 2",
		weight = 50,
		stack = true,
		close = true,
		description = "God of War 2 Game",
		client = {
			image = "gow2.png",
		}
	},

	["botlego"] = {
		label = "Bot Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Bot Lego Figure",
		client = {
			image = "botlego.png",
		}
	},

	["dnakit"] = {
		label = "DNA Kit",
		weight = 20,
		stack = true,
		close = true,
		description = "A DNA Kit",
		client = {
			image = "dnakit.png",
		}
	},

	["spacemanlego"] = {
		label = "Spaceman Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Spaceman Lego Figure",
		client = {
			image = "spacemanlego.png",
		}
	},

	["hotdoglego"] = {
		label = "Hotdog Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Hotdog Lego Figure",
		client = {
			image = "hotdoglego.png",
		}
	},

	["sw2"] = {
		label = "Samurai Warriors 2",
		weight = 50,
		stack = true,
		close = true,
		description = "Samurai Warriors 2 Game",
		client = {
			image = "sw2.png",
		}
	},

	["pearl"] = {
		label = "Pearl",
		weight = 5,
		stack = true,
		close = true,
		description = "A Pearl",
		client = {
			image = "pearl.png",
		}
	},

	["darksouls"] = {
		label = "Dark Souls",
		weight = 50,
		stack = true,
		close = true,
		description = "Dark Souls Game",
		client = {
			image = "darksouls.png",
		}
	},

	["dressshoes"] = {
		label = "Dress Shoes",
		weight = 50,
		stack = true,
		close = true,
		description = "A Pair of Dress Shoes",
		client = {
			image = "dressshoes.png",
		}
	},

	["silvercup"] = {
		label = "Silver Cup",
		weight = 40,
		stack = true,
		close = true,
		description = "A Silver Cup",
		client = {
			image = "silvercup.png",
		}
	},

	["loudspeakers"] = {
		label = "Loudspeakers",
		weight = 700,
		stack = true,
		close = true,
		description = "High-quality loudspeakers",
		client = {
			image = "loudspeakers.png",
		}
	},

	["rocklego"] = {
		label = "Rock Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Rock Lego Figure",
		client = {
			image = "rocklego.png",
		}
	},

	["crash"] = {
		label = "Crash Bandicoot",
		weight = 50,
		stack = true,
		close = true,
		description = "Crash Bandicoot Game",
		client = {
			image = "crash.png",
		}
	},

	["redninjalego"] = {
		label = "Red Ninja Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Red Ninja Lego Figure",
		client = {
			image = "redninjalego.png",
		}
	},

	["drone"] = {
		label = "Drone",
		weight = 80,
		stack = true,
		close = true,
		description = "A Drone",
		client = {
			image = "drone.png",
		}
	},

	["soundbar"] = {
		label = "Soundbar",
		weight = 600,
		stack = true,
		close = true,
		description = "Soundbar for enhanced audio",
		client = {
			image = "soundbar.png",
		}
	},

	["ibanez2"] = {
		label = "Vintage Ibanez Guitar",
		weight = 150,
		stack = true,
		close = true,
		description = "Ibanez Guitar 2",
		client = {
			image = "ibanez2.png",
		}
	},

	["oldradio"] = {
		label = "Old Radio",
		weight = 100,
		stack = true,
		close = true,
		description = "An Old Radio",
		client = {
			image = "oldradio.png",
		}
	},

	["sly"] = {
		label = "Sly Cooper",
		weight = 50,
		stack = true,
		close = true,
		description = "Sly Cooper Game",
		client = {
			image = "sly.png",
		}
	},

	["4klcdtv"] = {
		label = "4K LCD TV",
		weight = 1500,
		stack = true,
		close = true,
		description = "High-definition 4K LCD Television",
		client = {
			image = "4klcdtv.png",
		}
	},

	["golfclubs"] = {
		label = "Golf Clubs",
		weight = 150,
		stack = true,
		close = true,
		description = "A Set of Golf Clubs",
		client = {
			image = "golfclubs.png",
		}
	},

	["hulklego"] = {
		label = "Hulk Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Hulk Lego Figure",
		client = {
			image = "hulklego.png",
		}
	},

	["amazonecho"] = {
		label = "Amazon Echo",
		weight = 100,
		stack = true,
		close = true,
		description = "Amazon Echo",
		client = {
			image = "amazonecho.png",
		}
	},

	["legoset"] = {
		label = "Lego Set",
		weight = 150,
		stack = true,
		close = true,
		description = "A Lego Set",
		client = {
			image = "legoset.png",
		}
	},

	["tonyhawkproskater3"] = {
		label = "Tony Hawk's Pro Skater 3",
		weight = 50,
		stack = true,
		close = true,
		description = "Tony Hawk's Pro Skater 3 Game",
		client = {
			image = "tonyhawkproskater3.png",
		}
	},

	["batmanlego"] = {
		label = "Batman Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Batman Lego Figure",
		client = {
			image = "batmanlego.png",
		}
	},

	["uncharted2"] = {
		label = "Uncharted 2",
		weight = 50,
		stack = true,
		close = true,
		description = "Uncharted 2 Game",
		client = {
			image = "uncharted2.png",
		}
	},

	["sl1200"] = {
		label = "Technics SL-1200",
		weight = 500,
		stack = true,
		close = true,
		description = "Technics SL-1200 Turntable",
		client = {
			image = "sl1200.png",
		}
	},

	["driver"] = {
		label = "Driver",
		weight = 50,
		stack = true,
		close = true,
		description = "Driver Game",
		client = {
			image = "driver.png",
		}
	},

	["obiwanlego"] = {
		label = "Obi-Wan Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Obi-Wan Lego Figure",
		client = {
			image = "obiwanlego.png",
		}
	},

	["antmanlego"] = {
		label = "Ant-Man Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Ant-Man Lego Figure",
		client = {
			image = "antmanlego.png",
		}
	},

	["macbook"] = {
		label = "Macbook",
		weight = 80,
		stack = true,
		close = true,
		description = "A Macbook",
		client = {
			image = "macbook.png",
		}
	},

	["case2"] = {
		label = "Case 2",
		weight = 100,
		stack = true,
		close = true,
		description = "A Case 2",
		client = {
			image = "case2.png",
		}
	},

	["gta4"] = {
		label = "Grand Theft Auto IV",
		weight = 50,
		stack = true,
		close = true,
		description = "Grand Theft Auto IV Game",
		client = {
			image = "gta4.png",
		}
	},

	["case"] = {
		label = "Case",
		weight = 100,
		stack = true,
		close = true,
		description = "A Case",
		client = {
			image = "case.png",
		}
	},

	["ps3"] = {
		label = "PlayStation 3",
		weight = 100,
		stack = true,
		close = true,
		description = "PlayStation 3 Console",
		client = {
			image = "ps3.png",
		}
	},

	["ninjaturtlelego"] = {
		label = "Ninja Turtle Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Ninja Turtle Lego Figure",
		client = {
			image = "ninjaturtlelego.png",
		}
	},

	["orgelego"] = {
		label = "Orge Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Orge Lego Figure",
		client = {
			image = "orgelego.png",
		}
	},

	["orangeninjalego"] = {
		label = "Orange Ninja Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Orange Ninja Lego Figure",
		client = {
			image = "orangeninjalego.png",
		}
	},

	["fortunecat"] = {
		label = "Fortune Cat",
		weight = 40,
		stack = true,
		close = true,
		description = "A Fortune Cat Figure",
		client = {
			image = "fortunecat.png",
		}
	},

	["haloreach"] = {
		label = "Halo: Reach",
		weight = 50,
		stack = true,
		close = true,
		description = "Halo: Reach Game",
		client = {
			image = "haloreach.png",
		}
	},

	["gopro"] = {
		label = "GoPro",
		weight = 40,
		stack = true,
		close = true,
		description = "A GoPro Camera",
		client = {
			image = "gopro.png",
		}
	},

	["borderlands2"] = {
		label = "Borderlands 2",
		weight = 50,
		stack = true,
		close = true,
		description = "Borderlands 2 Game",
		client = {
			image = "borderlands2.png",
		}
	},

	["tlou"] = {
		label = "The Last of Us",
		weight = 50,
		stack = true,
		close = true,
		description = "The Last of Us Game",
		client = {
			image = "tlou.png",
		}
	},

	["barblego"] = {
		label = "Barb Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Barb Lego Figure",
		client = {
			image = "barblego.png",
		}
	},

	["nes"] = {
		label = "NES Console",
		weight = 80,
		stack = true,
		close = true,
		description = "A NES Console",
		client = {
			image = "nes.png",
		}
	},

	["rubix"] = {
		label = "Rubix Cube",
		weight = 20,
		stack = true,
		close = true,
		description = "A Rubix Cube",
		client = {
			image = "rubix.png",
		}
	},

	["skateboard"] = {
		label = "Skateboard",
		weight = 100,
		stack = true,
		close = true,
		description = "A Skateboard",
		client = {
			image = "skateboard.png",
		}
	},

	["l4d2"] = {
		label = "Left 4 Dead 2",
		weight = 50,
		stack = true,
		close = true,
		description = "Left 4 Dead 2 Game",
		client = {
			image = "l4d2.png",
		}
	},

	["rally3"] = {
		label = "Rally 3",
		weight = 50,
		stack = true,
		close = true,
		description = "Rally 3 Game",
		client = {
			image = "rally3.png",
		}
	},

	["crateofrecords"] = {
		label = "Crate of Records",
		weight = 300,
		stack = true,
		close = true,
		description = "Crate containing a collection of vinyl records",
		client = {
			image = "crateofrecords.png",
		}
	},

	["gt4"] = {
		label = "Gran Turismo 4",
		weight = 50,
		stack = true,
		close = true,
		description = "Gran Turismo 4 Game",
		client = {
			image = "gt4.png",
		}
	},

	["airpods"] = {
		label = "AirPods",
		weight = 10,
		stack = true,
		close = true,
		description = "Apple AirPods",
		client = {
			image = "airpods.png",
		}
	},

	["xbox360"] = {
		label = "Xbox 360",
		weight = 100,
		stack = true,
		close = true,
		description = "Xbox 360 Console",
		client = {
			image = "xbox360.png",
		}
	},

	["farcry3"] = {
		label = "Far Cry 3",
		weight = 50,
		stack = true,
		close = true,
		description = "Far Cry 3 Game",
		client = {
			image = "farcry3.png",
		}
	},

	["tloz"] = {
		label = "The Legend of Zelda",
		weight = 50,
		stack = true,
		close = true,
		description = "The Legend of Zelda Game",
		client = {
			image = "tloz.png",
		}
	},

	["midnightclubla"] = {
		label = "Midnight Club LA",
		weight = 50,
		stack = true,
		close = true,
		description = "Midnight Club LA Game",
		client = {
			image = "midnightclubla.png",
		}
	},

	["xbox1"] = {
		label = "Xbox One",
		weight = 100,
		stack = true,
		close = true,
		description = "An Xbox One Console",
		client = {
			image = "xbox.png",
		}
	},

	["haloinfinite"] = {
		label = "Halo Infinite",
		weight = 50,
		stack = true,
		close = true,
		description = "Halo Infinite Game",
		client = {
			image = "haloinfinite.png",
		}
	},

	["harrypotterlego"] = {
		label = "Harry Potter Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Harry Potter Lego Figure",
		client = {
			image = "harrypotterlego.png",
		}
	},

	["nomanssky"] = {
		label = "No Man's Sky",
		weight = 50,
		stack = true,
		close = true,
		description = "No Man's Sky Game",
		client = {
			image = "nomanssky.png",
		}
	},

	["switch"] = {
		label = "Nintendo Switch",
		weight = 60,
		stack = true,
		close = true,
		description = "A Nintendo Switch Console",
		client = {
			image = "switch.png",
		}
	},

	["ps1"] = {
		label = "PlayStation 1",
		weight = 100,
		stack = true,
		close = true,
		description = "PlayStation 1 Console",
		client = {
			image = "ps1.png",
		}
	},

	["knightlego"] = {
		label = "Knight Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Knight Lego Figure",
		client = {
			image = "knightlego.png",
		}
	},

	["yodalego"] = {
		label = "Yoda Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Yoda Lego Figure",
		client = {
			image = "yodalego.png",
		}
	},

	["headphones"] = {
		label = "Headphones",
		weight = 50,
		stack = true,
		close = true,
		description = "A Pair of Headphones",
		client = {
			image = "headphones.png",
		}
	},

	["oldtv"] = {
		label = "Old CRT TV",
		weight = 1500,
		stack = true,
		close = true,
		description = "Old CRT Television",
		client = {
			image = "oldtv.png",
		}
	},

	["spyro"] = {
		label = "Spyro the Dragon",
		weight = 50,
		stack = true,
		close = true,
		description = "Spyro the Dragon Game",
		client = {
			image = "spyro.png",
		}
	},

	["amazondot"] = {
		label = "Amazon Dot",
		weight = 50,
		stack = true,
		close = true,
		description = "Amazon Echo Dot",
		client = {
			image = "amazondot.png",
		}
	},

	["snakeboots"] = {
		label = "Snake Boots",
		weight = 100,
		stack = true,
		close = true,
		description = "A pair of Snake Boots",
		client = {
			image = "snakeboots.png",
		}
	},

	["fh4"] = {
		label = "Forza Horizon 4",
		weight = 50,
		stack = true,
		close = true,
		description = "Forza Horizon 4 Game",
		client = {
			image = "fh4.png",
		}
	},

	["dw"] = {
		label = "Dragon Warriors",
		weight = 50,
		stack = true,
		close = true,
		description = "Dragon Warriors Game",
		client = {
			image = "dw.png",
		}
	},

	["codmw2"] = {
		label = "Call of Duty: Modern Warfare 2",
		weight = 50,
		stack = true,
		close = true,
		description = "Call of Duty: Modern Warfare 2 Game",
		client = {
			image = "codmw2.png",
		}
	},

	["assassinscreed2"] = {
		label = "Assassin's Creed 2",
		weight = 50,
		stack = true,
		close = true,
		description = "Assassin's Creed 2 Game",
		client = {
			image = "assassinscreed2.png",
		}
	},

	["heavyrain"] = {
		label = "Heavy Rain",
		weight = 50,
		stack = true,
		close = true,
		description = "Heavy Rain Game",
		client = {
			image = "heavyrain.png",
		}
	},

	["skate3"] = {
		label = "Skate 3",
		weight = 50,
		stack = true,
		close = true,
		description = "Skate 3 Game",
		client = {
			image = "skate3.png",
		}
	},

	["ankianlego"] = {
		label = "Anakin Skywalker Lego",
		weight = 30,
		stack = true,
		close = true,
		description = "Ankian Lego Figure",
		client = {
			image = "ankianlego.png",
		}
	},

	["simpsonshitandrun"] = {
		label = "The Simpsons: Hit & Run",
		weight = 50,
		stack = true,
		close = true,
		description = "The Simpsons: Hit & Run Game",
		client = {
			image = "simpsonshitandrun.png",
		}
	},

	["goldpocketwatch"] = {
		label = "Gold Pocket Watch",
		weight = 15,
		stack = true,
		close = true,
		description = "A Gold Pocket Watch",
		client = {
			image = "goldpocketwatch.png",
		}
	},

	["pc"] = {
		label = "PC",
		weight = 100,
		stack = true,
		close = true,
		description = "A Personal Computer",
		client = {
			image = "pc.png",
		}
	},

	["buzz"] = {
		label = "Buzz Lightyear",
		weight = 40,
		stack = true,
		close = true,
		description = "Buzz Lightyear Figure",
		client = {
			image = "buzz.png",
		}
	},

	["magnet"] = {
		label = "Magnet",
		weight = 100,
		stack = false,
		close = true,
		description = "A powerful magnet used for magnet fishing.",
		client = {
			image = "magnet.png",
		}
	},

	["hide_mtlion"] = {
		label = "Mt Lion Hide",
		weight = 1000,
		stack = true,
		close = false,
		description = "A intact Mt Lion Hide. You should sell this.",
		client = {
			image = "hide_mtlion.png",
		}
	},

	["meat_boar"] = {
		label = "Boar Meat",
		weight = 600,
		stack = true,
		close = false,
		description = "A piece of Boar Meat. You should sell this.",
		client = {
			image = "meat_boar.png",
		}
	},

	["hide_shark"] = {
		label = "Shark Hide",
		weight = 1500,
		stack = true,
		close = false,
		description = "A intact Shark Hide. You should sell this.",
		client = {
			image = "hide_shark.png",
		}
	},

	["meat_coyote"] = {
		label = "Coyote Meat",
		weight = 500,
		stack = true,
		close = false,
		description = "A piece of Coyote Meat. You should sell this.",
		client = {
			image = "meat_coyote.png",
		}
	},

	["hide_rat"] = {
		label = "Rat Hide",
		weight = 300,
		stack = true,
		close = false,
		description = "A intact Rat Hide. You should sell this.",
		client = {
			image = "hide_rat.png",
		}
	},

	["hide_boar"] = {
		label = "Boar Hide",
		weight = 1100,
		stack = true,
		close = false,
		description = "A intact Boar Hide. You should sell this.",
		client = {
			image = "hide_boar.png",
		}
	},

	["hide_rabbit"] = {
		label = "Rabbit Hide",
		weight = 300,
		stack = true,
		close = false,
		description = "A intact Rabbit Hide. You should sell this.",
		client = {
			image = "hide_rabbit.png",
		}
	},

	["meat_stingray"] = {
		label = "Stingray Meat",
		weight = 450,
		stack = true,
		close = false,
		description = "A piece of Stingray Meat. You should sell this.",
		client = {
			image = "meat_stingray.png",
		}
	},

	["meat_rat"] = {
		label = "Rat Meat",
		weight = 100,
		stack = true,
		close = false,
		description = "A piece of Rat Meat. You should sell this.",
		client = {
			image = "meat_rat.png",
		}
	},

	["meat_mtlion"] = {
		label = "Mt Lion Meat",
		weight = 650,
		stack = true,
		close = false,
		description = "A piece of Mt Lion Meat. You should sell this.",
		client = {
			image = "meat_mtlion.png",
		}
	},

	["meat_deer"] = {
		label = "Deer Meat",
		weight = 300,
		stack = true,
		close = false,
		description = "A piece of Deer Meat. You should sell this.",
		client = {
			image = "meat_deer.png",
		}
	},

	["hide_cow"] = {
		label = "Cow Hide",
		weight = 1300,
		stack = true,
		close = false,
		description = "A intact Cow Hide. You should sell this.",
		client = {
			image = "hide_cow.png",
		}
	},

	["meat_dolphin"] = {
		label = "Dolphin Meat",
		weight = 750,
		stack = true,
		close = false,
		description = "A piece of Dolphin Meat. You should sell this.",
		client = {
			image = "meat_dolphin.png",
		}
	},

	["meat_whale"] = {
		label = "Whale Meat",
		weight = 1200,
		stack = true,
		close = false,
		description = "A piece of Whale Meat. You should sell this.",
		client = {
			image = "meat_whale.png",
		}
	},

	["hide_dolphin"] = {
		label = "Dolphin Hide",
		weight = 1250,
		stack = true,
		close = false,
		description = "A intact Dolphin Hide. You should sell this.",
		client = {
			image = "hide_dolphin.png",
		}
	},

	["hide_coyote"] = {
		label = "Coyote Hide",
		weight = 800,
		stack = true,
		close = false,
		description = "A intact Coyote Hide. You should sell this.",
		client = {
			image = "hide_coyote.png",
		}
	},

	["meat_shark"] = {
		label = "Shark Meat",
		weight = 900,
		stack = true,
		close = false,
		description = "A piece of Shark Meat. You should sell this.",
		client = {
			image = "meat_shark.png",
		}
	},

	["hide_stingray"] = {
		label = "Stingray Hide",
		weight = 850,
		stack = true,
		close = false,
		description = "A intact Stingray Hide. You should sell this.",
		client = {
			image = "hide_stingray.png",
		}
	},

	["hide_whale"] = {
		label = "Whale Hide",
		weight = 2000,
		stack = true,
		close = false,
		description = "A intact Whale Hide. You should sell this.",
		client = {
			image = "hide_whale.png",
		}
	},

	["hide_deer"] = {
		label = "Deer Hide",
		weight = 500,
		stack = true,
		close = false,
		description = "A intact Deer Hide. You should sell this.",
		client = {
			image = "hide_deer.png",
		}
	},

	["meat_cow"] = {
		label = "Cow Meat",
		weight = 800,
		stack = true,
		close = false,
		description = "A piece of Cow Meat. You should sell this.",
		client = {
			image = "meat_cow.png",
		}
	},

	["meat_rabbit"] = {
		label = "Rabbit Meat",
		weight = 200,
		stack = true,
		close = false,
		description = "A piece of Rabbit Meat. You should sell this.",
		client = {
			image = "meat_rabbit.png",
		}
	},

	["skin_deer_ruined"] = {
		label = "Tattered Deer Pelt",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "skin_deer_ruined.png",
		}
	},

	["deer_horn"] = {
		label = "Deer Horn",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "deer_horn.png",
		}
	},

	["raw_meat"] = {
		label = "Raw Meat",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "raw_meat.png",
		}
	},

	["skin_deer_good"] = {
		label = "Prime Deer Pelt",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "skin_deer_good.png",
		}
	},

	["cooked_meat"] = {
		label = "Cooked Meat",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "cooked_meat.png",
		}
	},

	["animal_tracker"] = {
		label = "Animal Tracker",
		weight = 200,
		stack = true,
		close = true,
		description = "",
		client = {
			image = "animal_tracker.png",
		}
	},

	["huntingbait"] = {
		label = "Hunting Bait",
		weight = 200,
		stack = true,
		close = true,
		description = "",
		client = {
			image = "huntingbait.png",
		}
	},

	["campfire"] = {
		label = "Campfire",
		weight = 200,
		stack = true,
		close = true,
		description = "",
		client = {
			image = "campfire.png",
		}
	},

	["skin_deer_low"] = {
		label = "Worn Deer Pelt",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "skin_deer_low.png",
		}
	},

	["skin_deer_perfect"] = {
		label = "Flawless Deer Pelt",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "skin_deer_perfect.png",
		}
	},

	["skin_deer_medium"] = {
		label = "Supple Deer Pelt",
		weight = 200,
		stack = true,
		close = false,
		description = "",
		client = {
			image = "skin_deer_medium.png",
		}
	},

	["sprayremover"] = {
		label = "Spray Remover",
		weight = 100,
		stack = false,
		close = true,
		description = "Spray Remover",
		client = {
			image = "sprayremover.png",
		}
	},

	["spraycan"] = {
		label = "Spray Can",
		weight = 1000,
		stack = false,
		close = true,
		description = "Spray Can",
		client = {
			image = "spraycan.png",
		}
	},
	['wheelclamp'] = {
	label       = 'Wheel Clamp',
	weight      = 500,
	stack       = false,
	close       = true,
	description = 'A device used to immobilize vehicle wheels.',
	client = {
		image = "wheelclamp.png",
	}

	},
	['anglegrinder'] = {
	label       = 'Angle Grinder',
	weight      = 500,
	stack       = false,
	close       = true,
	description = 'A handy tool for cutting through metal.',
		client = {
		image = "anglegrinder.png",
	}
	},

	["red_phone"] = {
		label = "Red Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "red_phone.png",
		}
	},

	["wet_white_phone"] = {
		label = "Wet White Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "wet_white_phone.png",
		}
	},

	["white_phone"] = {
		label = "White Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "white_phone.png",
		}
	},

	["wet_black_phone"] = {
		label = "Wet Black Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "Did you really think that swimming in the ocean with your phone was a good idea?",
		client = {
			image = "wet_black_phone.png",
		}
	},

	["phone_hack"] = {
		label = "Phone Hack",
		weight = 300,
		stack = false,
		close = true,
		description = "With this chip, you can access hidden areas of Discord.",
		client = {
			image = "phone_hack.png",
		}
	},

	["gold_phone"] = {
		label = "Gold Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "gold_phone.png",
		}
	},

	["purple_phone"] = {
		label = "Purple Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "purple_phone.png",
		}
	},

	["phone_module"] = {
		label = "Phone Module",
		weight = 300,
		stack = false,
		close = true,
		description = "It seems that we can fix a wet phone with this module, interesting.",
		client = {
			image = "phone_module.png",
		}
	},

	["classic_phone"] = {
		label = "Classic Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "classic_phone.png",
		}
	},

	["wet_classic_phone"] = {
		label = "Wet Classic Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "Did you really think that swimming in the ocean with your phone was a good idea?",
		client = {
			image = "wet_classic_phone.png",
		}
	},

	["greenlight_phone"] = {
		label = "Green Light Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "greenlight_phone.png",
		}
	},

	["green_phone"] = {
		label = "Green Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "green_phone.png",
		}
	},

	["black_phone"] = {
		label = "Black Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "black_phone.png",
		}
	},

	["wet_blue_phone"] = {
		label = "Wet Blue Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "Did you really think that swimming in the ocean with your phone was a good idea?",
		client = {
			image = "wet_blue_phone.png",
		}
	},

	["wet_purple_phone"] = {
		label = "Wet Purple Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "Did you really think that swimming in the ocean with your phone was a good idea?",
		client = {
			image = "wet_purple_phone.png",
		}
	},

	["wet_green_phone"] = {
		label = "Wet Green Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "wet_green_phone.png",
		}
	},

	["wet_red_phone"] = {
		label = "Wet Red Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "Did you really think that swimming in the ocean with your phone was a good idea?",
		client = {
			image = "wet_red_phone.png",
		}
	},

	["blue_phone"] = {
		label = "Blue Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "blue_phone.png",
		}
	},

	["pink_phone"] = {
		label = "Pink Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "pink_phone.png",
		}
	},

	["wet_pink_phone"] = {
		label = "Wet Pink Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "wet_pink_phone.png",
		}
	},

	["wet_gold_phone"] = {
		label = "Wet Gold Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "Did you really think that swimming in the ocean with your phone was a good idea?",
		client = {
			image = "wet_gold_phone.png",
		}
	},

	["wet_greenlight_phone"] = {
		label = "Wet Green Light Phone",
		weight = 700,
		stack = false,
		close = true,
		description = "They say that Quasar Smartphone is the same as an iPhone, what do you think?",
		client = {
			image = "wet_greenlight_phone.png",
		}
	},
	-- Rup Fishing Items

	['fishingrod'] = {
		label = 'Fishing Rod',
		weight = 800,
		stack = true,
		close = true,
		client = {
			export = 'zg-fishing.useRod',
		}
	},

	["fishbait"] = {
		label = "Fishing Bait",
		weight = 50,
		stack = true,
		close = false,
		description = "Use this to start fishing",
	},

	["anchovy"] = {
		label = "Anchovy",
		weight = 200,
		stack = true,
		close = false,
		description = "An Anchovy caught fishing",
	},

	["barracuda"] = {
		label = "Barracuda",
		weight = 200,
		stack = true,
		close = false,
		description = "A Barracuda caught fishing",
	},

	["bass"] = {
		label = "Bass",
		weight = 200,
		stack = true,
		close = false,
		description = "A Bass caught fishing",
	},

	["bluefish"] = {
		label = "Bluefish",
		weight = 200,
		stack = true,
		close = false,
		description = "A Bluefish caught fishing",
	},

	["bream"] = {
		label = "Bream",
		weight = 200,
		stack = true,
		close = false,
		description = "A Bream caught fishing",
	},

	["carp"] = {
		label = "Carp",
		weight = 200,
		stack = true,
		close = false,
		description = "A Carp caught fishing",
	},

	["catfish"] = {
		label = "Catfish",
		weight = 200,
		stack = true,
		close = false,
		description = "A Catfish caught fishing",
	},

	["char"] = {
		label = "Char",
		weight = 200,
		stack = true,
		close = false,
		description = "A Char caught fishing",
	},

	["cod"] = {
		label = "Cod",
		weight = 200,
		stack = true,
		close = false,
		description = "A Cod caught fishing",
	},

	["eel"] = {
		label = "Eel",
		weight = 200,
		stack = true,
		close = false,
		description = "An Eel caught fishing",
	},

	["flounder"] = {
		label = "Flounder",
		weight = 200,
		stack = true,
		close = false,
		description = "A Flounder caught fishing",
	},

	["grouper"] = {
		label = "Grouper",
		weight = 200,
		stack = true,
		close = false,
		description = "A Grouper caught fishing",
	},

	["haddock"] = {
		label = "Haddock",
		weight = 200,
		stack = true,
		close = false,
		description = "A Haddock caught fishing",
	},

	["halibut"] = {
		label = "Halibut",
		weight = 200,
		stack = true,
		close = false,
		description = "A Halibut caught fishing",
	},

	["herring"] = {
		label = "Herring",
		weight = 200,
		stack = true,
		close = false,
		description = "A Herring caught fishing",
	},

	["mackerel"] = {
		label = "Mackerel",
		weight = 200,
		stack = true,
		close = false,
		description = "A Mackerel caught fishing",
	},

	["marlin"] = {
		label = "Marlin",
		weight = 200,
		stack = true,
		close = false,
		description = "A Marlin caught fishing",
	},

	["perch"] = {
		label = "Perch",
		weight = 200,
		stack = true,
		close = false,
		description = "A Perch caught fishing",
	},

	["pike"] = {
		label = "Pike",
		weight = 200,
		stack = true,
		close = false,
		description = "A Pike caught fishing",
	},

	["pollock"] = {
		label = "Pollock",
		weight = 200,
		stack = true,
		close = false,
		description = "A Pollock caught fishing",
	},

	["redfish"] = {
		label = "Redfish",
		weight = 200,
		stack = true,
		close = false,
		description = "A Redfish caught fishing",
	},

	["salmon"] = {
		label = "Salmon",
		weight = 200,
		stack = true,
		close = false,
		description = "A Salmon caught fishing",
	},

	["sardine"] = {
		label = "Sardine",
		weight = 200,
		stack = true,
		close = false,
		description = "A Sardine caught fishing",
	},

	["shad"] = {
		label = "Shad",
		weight = 200,
		stack = true,
		close = false,
		description = "A Shad caught fishing",
	},

	["snapper"] = {
		label = "Snapper",
		weight = 200,
		stack = true,
		close = false,
		description = "A Snapper caught fishing",
	},

	["sturgeon"] = {
		label = "Sturgeon",
		weight = 200,
		stack = true,
		close = false,
		description = "A Sturgeon caught fishing",
	},

	["swordfish"] = {
		label = "Swordfish",
		weight = 200,
		stack = true,
		close = false,
		description = "A Swordfish caught fishing",
	},

	["tilapia"] = {
		label = "Tilapia",
		weight = 200,
		stack = true,
		close = false,
		description = "A Tilapia caught fishing",
	},

	["trout"] = {
		label = "Trout",
		weight = 200,
		stack = true,
		close = false,
		description = "A Trout caught fishing",
	},

	["tuna"] = {
		label = "Tuna",
		weight = 200,
		stack = true,
		close = false,
		description = "A Tuna caught fishing",
	},

	["walleye"] = {
		label = "Walleye",
		weight = 200,
		stack = true,
		close = false,
		description = "A Walleye caught fishing",
	},


	["houselaptop"] = {
        label = "House laptop",
        weight = 1200,
        stack = true,
        close = false,
        description = "Can Probably Hack Something With This",
        client = {
            image = "houselaptop.png",
        }
    },
	["mansionlaptop"] = {
        label = "Mansion laptop",
        weight = 1200,
        stack = true,
        close = false,
        description = "Can Probably Hack Something With This",
        client = {
            image = "mansionlaptop.png",
        }
    },
	["art1"] = {
        label = "Kitty Sleeping Art",
        weight = 2500,
        stack = true,
        close = false,
        description = "This Is Too Cute",
        client = {
            image = "art1.png",
        }
    },
	["art2"] = {
        label = "Wide Eye Kitty Art",
        weight = 2500,
        stack = true,
        close = false,
        description = "This Is Too Cute",
        client = {
            image = "art2.png",
        }
    },
	["art3"] = {
        label = "Fancy Kitty Art",
        weight = 2500,
        stack = true,
        close = false,
        description = "This Is Too Cute",
        client = {
            image = "art3.png",
        }
    },
	["art4"] = {
        label = "Presidential Kitty Art",
        weight = 2500,
        stack = true,
        close = false,
        description = "Id Vote For This",
        client = {
            image = "art4.png",
        }
    },
	["art5"] = {
        label = "Obi Jesus Painting",
        weight = 2500,
        stack = true,
        close = false,
        description = "I Swore My Allegiance To The Force, To Heaven!",
        client = {
            image = "art5.png",
        }
    },
	["art6"] = {
        label = "Merp Kitty Art",
        weight = 2500,
        stack = true,
        close = false,
        description = "Merp",
        client = {
            image = "art6.png",
        }
    },
	["art7"] = {
        label = "Family Portait",
        weight = 2500,
        stack = true,
        close = false,
        description = "Smile",
        client = {
            image = "art7.png",
        }
    },
	["boombox"] = {
        label = "Boom Box",
        weight = 2500,
        stack = true,
        close = false,
        description = "How Did People Carry This",
        client = {
            image = "boombox.png",
        }
    },
	["checkbook"] = {
        label = "Check Book",
        weight = 2500,
        stack = true,
        close = false,
        description = "Do People Use These?",
        client = {
            image = "checkbook.png",
        }
    },
	["mdlaptop"] = {
        label = "Slow Laptop",
        weight = 2500,
        stack = true,
        close = false,
        description = "Can I Download More Ram?",
        client = {
            image = "laptop.png",
        }
    },
	["mddesktop"] = {
        label = "Desktop",
        weight = 2500,
        stack = true,
        close = false,
        description = "I hope there isnt a virus",
        client = {
            image = "mddesktop.png",
        }
    },
	["mdmonitor"] = {
        label = "Monitor",
        weight = 2500,
        stack = true,
        close = false,
        description = "720HD bb",
        client = {
            image = "mdmonitor.png",
        }
    },
	["mdtablet"] = {
        label = "Tablet",
        weight = 2500,
        stack = true,
        close = false,
        description = "Never Will Give This Up",
        client = {
            image = "mdtablet.png",
        }
    },
	["mdspeakers"] = {
        label = "Speakers",
        weight = 2500,
        stack = true,
        close = false,
        description = "Is This Even Loud?",
        client = {
            image = "speaker.png",
        }
    },

	["fries"] = {
		label = "Fries",
		weight = 100,
		type = "item",
		image = "fries.png",
		unique = false,
		useable = true,
		shouldClose = true,
		description = "A tasty serving of fries"
	},

	["pizza"] = {
		label = "Pizza",
		weight = 500,
		type = "item",
		image = "pizza.png",
		unique = false,
		useable = true,
		shouldClose = true,
		description = "A delicious pizza slice"
	},

	["hotdog"] = {
		label = "Hotdog",
		weight = 200,
		type = "item",
		image = "hotdog.png",
		unique = false,
		useable = true,
		shouldClose = true,
		description = "A classic hotdog"
	},

	["cake"] = {
		label = "Cake",
		weight = 400,
		type = "item",
		image = "cake.png",
		unique = false,
		useable = true,
		shouldClose = true,
		description = "A sweet cake"
	},

	["donut"] = {
		label = "Donut",
		weight = 150,
		type = "item",
		image = "donut.png",
		unique = false,
		useable = true,
		shouldClose = true,
		description = "A tasty donut"
	},


	["art8"] = {
		label = "Art Piece 8",
		weight = 1000,
		type = "item",
		image = "art8.png",
		unique = true,
		useable = false,
		shouldClose = true,
		description = "A beautiful piece of art"
	},

	["art9"] = {
		label = "Art Piece 9",
		weight = 1000,
		type = "item",
		image = "art9.png",
		unique = true,
		useable = false,
		shouldClose = true,
		description = "A beautiful piece of art"
	},

    -- Basic Cards
    ["blightthornscavenger"] = {
        label = "Blightthorn Scavenger",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "blightthornscavenger.png" },
    },
    ["cliffsidetroll"] = {
        label = "Cliffside Troll",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "cliffsidetroll.png" },
    },
    ["frostlingtrickster"] = {
        label = "Frostling Trickster",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "frostlingtrickster.png" },
    },
    ["grimebacklucker"] = {
        label = "Grimeback Lucker",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "grimebacklucker.png" },
    },
    ["gutterspawnimp"] = {
        label = "Gutterspawn Imp",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "gutterspawnimp.png" },
    },
    ["ironbeakraptor"] = {
        label = "Ironbeak Raptor",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "ironbeakraptor.png" },
    },
    ["marrowgnashhound"] = {
        label = "Marrowgnash Hound",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "marrowgnashhound.png" },
    },
    ["lavafistgoblin"] = {
        label = "Lavafist Goblin",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "lavafistgoblin.png" },
    },
    ["rubblefistgrunt"] = {
        label = "Rubblefist Grunt",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "rubblefistgrunt.png" },
    },
    ["torchgutogre"] = {
        label = "Torchgut Ogre",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "torchgutogre.png" },
    },

    -- Rare Cards
    ["chitinmawswarmlord"] = {
        label = "Chitinmaw Swarmlord",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "chitinmawswarmlord.png" },
    },
    ["duskwitherfiend"] = {
        label = "Duskwither Fiend",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "duskwitherfiend.png" },
    },
    ["fleshgnawbasilisk"] = {
        label = "Fleshgnaw Basilisk",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "fleshgnawbasilisk.png" },
    },
    ["gravetidehorror"] = {
        label = "Gravetide Horror",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "gravetidehorror.png" },
    },
    ["mechfangravager"] = {
        label = "Mechfang Ravager",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "mechfangravager.png" },
    },
    ["pyroskullcharger"] = {
        label = "Pyroskull Charger",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "pyroskullcharger.png" },
    },
    ["stormlashunicorn"] = {
        label = "Stormlash Unicorn",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "stormlashunicorn.png" },
    },

    -- Ultra Cards
    ["twilightdemon"] = {
        label = "Twilight Demon",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "twilightdemon.png" },
    },
    ["gorehowlravager"] = {
        label = "Gorehowl Ravager",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "gorehowlravager.png" },
    },
    ["cryoheartserpent"] = {
        label = "Cryoheart Serpent",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "cryoheartserpent.png" },
    },
    ["obsidianmauler"] = {
        label = "Obsidian Mauler",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "obsidianmauler.png" },
    },
    ["soulshredbanshee"] = {
        label = "Soulshred Banshee",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "soulshredbanshee.png" },
    },

    -- V Cards
    ["venomclawbehemoth"] = {
        label = "Venomclaw Behemoth",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "venomclawbehemoth.png" },
    },
    ["plaguequeen"] = {
        label = "Plague Queen",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "plaguequeen.png" },
    },
    ["thebanishedking"] = {
        label = "The Banished King",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "thebanishedking.png" },
    },
    ["stormgolem"] = {
        label = "Storm Golem",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "stormgolem.png" },
    },
    ["bonehydra"] = {
        label = "Bone Hydra",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "bonehydra.png" },
    },

    -- VMAX Cards
    ["flametyrant"] = {
        label = "Flame Tyrant",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "flametyrant.png" },
    },
    ["thenullbeast"] = {
        label = "The Null Beast",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "thenullbeast.png" },
    },

    -- Rainbow Card
    ["voidreaper"] = {
        label = "Void Reaper",
        weight = 0,
        stack = true,
        close = false,
        description = "",
        client = { image = "voidreaper.png" },
    },

    ["boosterpack"] = {
		label = "Boosterpack",
		weight = 0,
		stack = true,
		close = true,
		description = "Pack of Cards",
		client = {
			image = "boosterPack.png",
		}
	},

    ["boosterbox"] = {
		label = "Boosterbox",
		weight = 200,
		stack = true,
		close = true,
		description = "Box Of Card Packs",
		client = {
			image = "boosterBox.png",
		}
	},
    ["container_green_small"] = {
        label = "Small Green Container",
        weight = 5,
        stack = false,
        close = true,
        description = nil
     },

    ["container_blue_mid"] = {
        label = "Mid Blue Container",
        weight = 15,
        stack = false,
        close = true,
        description = nil
     },

    ["container_old_mid"] = {
        label = "Mid Old Container",
        weight = 15,
        stack = false,
        close = true,
        description = nil
     },

    ["container_white_mid"] = {
        label = "Mid White Container",
        weight = 15,
        stack = false,
        close = true,
        description = nil
     },

    ["containerboltcutter"] = {
        label = "Boltcutter",
        weight = 1,
        stack = false,
        close = false,
        description = 'a boltcutter to open containers by police'
     },
    -- Hunting License
    ['hunting_license'] = {
        label = 'Hunting License',
        weight = 50,
        stack = false,
        close = true,
        description = 'Official hunting license required for legal hunting activities',
        client = {
            image = 'hunting_license.png',
        }
    },

    -- Hunting Equipment
    ['hunting_bag'] = {
        label = 'Hunting Bag',
        weight = 2000,
        stack = false,
        close = true,
        description = 'A sturdy bag containing everything needed to set up a hunting camp',
        client = {
            image = 'hunting_bag.png',
        },
        server = {
            export = 'qbox-hunting.hunting_bag'
        }
    },

    ['hunting_bait'] = {
        label = 'Hunting Bait',
        weight = 500,
        stack = true,
        close = true,
        description = 'Special bait designed to attract wild animals',
        client = {
            image = 'hunting_bait.png',
        },
        server = {
            export = 'qbox-hunting.hunting_bait'
        }
    },

    ['weapon_knife'] = {
        label = 'Hunting Knife',
        weight = 300,
        stack = false,
        close = true,
        description = 'A sharp knife specifically designed for skinning animals',
        client = {
            image = 'weapon_knife.png',
        }
    },

    -- Animal Products
    ['raw_meat'] = {
        label = 'Raw Meat',
        weight = 200,
        stack = true,
        close = true,
        description = 'Fresh meat from a hunted animal. Can be cooked or sold.',
        client = {
            image = 'raw_meat.png',
        }
    },

    -- Quality-based Animal Pelts
    ['animal_pelt_poor'] = {
        label = '⭐ Poor Animal Pelt',
        weight = 800,
        stack = true,
        close = true,
        description = 'A damaged animal pelt with visible flaws. Low market value.',
        client = {
            image = 'animal_pelt_poor.png',
        }
    },

    ['animal_pelt_good'] = {
        label = '⭐⭐ Good Animal Pelt',
        weight = 800,
        stack = true,
        close = true,
        description = 'A well-preserved animal pelt with minor imperfections. Good market value.',
        client = {
            image = 'animal_pelt_good.png',
        }
    },

    ['animal_pelt_perfect'] = {
        label = '⭐⭐⭐ Perfect Animal Pelt',
        weight = 800,
        stack = true,
        close = true,
        description = 'A flawless animal pelt in pristine condition. Highest market value.',
        client = {
            image = 'animal_pelt_perfect.png',
        }
    },

    -- Processed Products (optional for future expansion)
    ['cooked_meat'] = {
        label = 'Cooked Meat',
        weight = 150,
        stack = true,
        close = true,
        description = 'Properly cooked meat that restores health when consumed',
        client = {
            image = 'cooked_meat.png',
        }
    },

    ['leather'] = {
        label = 'Leather',
        weight = 600,
        stack = true,
        close = true,
        description = 'Processed leather from animal pelts',
        client = {
            image = 'leather.png',
        }
    },

    -- Special Items (rare drops)
    ['deer_antlers'] = {
        label = 'Deer Antlers',
        weight = 1200,
        stack = true,
        close = true,
        description = 'Rare deer antlers that can be sold for a high price',
        client = {
            image = 'deer_antlers.png',
        }
    },

    ['mountain_lion_tooth'] = {
        label = 'Mountain Lion Tooth',
        weight = 50,
        stack = true,
        close = true,
        description = 'A rare trophy from a mountain lion',
        client = {
            image = 'mountain_lion_tooth.png',
        }
    },

    ['boar_tusk'] = {
        label = 'Boar Tusk',
        weight = 150,
        stack = true,
        close = true,
        description = 'Sharp tusk from a wild boar',
        client = {
            image = 'boar_tusk.png',
        }
    },

    -- Hunting Weapons
    ['weapon_sniperrifle'] = {
        label = 'Hunting Rifle',
        weight = 3000,
        stack = false,
        close = true,
        description = 'A high-powered rifle perfect for hunting large game',
        client = {
            image = 'weapon_sniperrifle.png',
        }
    },

    ['sniper_ammo'] = {
        label = 'Rifle Ammunition',
        weight = 50,
        stack = true,
        close = true,
        description = 'High-caliber ammunition for hunting rifles',
        client = {
            image = 'sniper_ammo.png',
        }
    },
	['medikit'] = { -- Make sure not already a medikit
		label = 'Medikit',
		weight = 165,
		stack = true,
		close = true,
	},
	['medbag'] = {
		label = 'Medical Bag',
		weight = 165,
		stack = false,
		close = true,
	},

	['tweezers'] = {
		label = 'Tweezers',
		weight = 2,
		stack = true,
		close = true,
	},

	['suturekit'] = {
		label = 'Suture Kit',
		weight = 15,
		stack = true,
		close = true,
	},

	['icepack'] = {
		label = 'Ice Pack',
		weight = 29,
		stack = true,
		close = true,
	},

	['burncream'] = {
		label = 'Burn Cream',
		weight = 19,
		stack = true,
		close = true,
	},

	['defib'] = {
		label = 'Defibrillator',
		weight = 225,
		stack = false,
		close = true,
	},

	['sedative'] = {
		label = 'Sedative',
		weight = 15,
		stack = true,
		close = true,
	},

	['morphine30'] = {
		label = 'Morphine 30MG',
		weight = 2,
		stack = true,
		close = true,
	},

	['morphine15'] = {
		label = 'Morphine 15MG',
		weight = 2,
		stack = true,
		close = true,
	},

	['perc30'] = {
		label = 'Percocet 30MG',
		weight = 2,
		stack = true,
		close = true,
	},

	['perc10'] = {
		label = 'Percocet 10MG',
		weight = 2,
		stack = true,
		close = true,
	},

	['perc5'] = {
		label = 'Percocet 5MG',
		weight = 2,
		stack = true,
		close = true,
	},

	['vic10'] = {
		label = 'Vicodin 10MG',
		weight = 2,
		stack = true,
		close = true,
	},

	['vic5'] = {
		label = 'Vicodin 5MG',
		weight = 2,
		stack = true,
		close = true,
	},

	['recoveredbullet'] = {
		label = 'Recovered Bullet',
		weight = 1,
		stack = true,
		close = false,
	},

	['bankcard'] = {
		label = 'Bank Card',
		weight = 1,
		stack = false,
		close = true,
	},

	['transactionsreceipt'] = {
		label = 'Transaction Receipt',
		weight = 1,
		stack = false,
		close = true,
	},


	['blackjack_table'] = {
		label = 'Blackjack Table',
		weight = 1,
		stack = true,
		close = true,
		description = nil
	},
	['baccarat_table'] = {
		label = 'Baccarat Table',
		weight = 1,
		stack = true,
		close = true,
		description = nil
	},
	['poker_table'] = {
		label = 'Poker Table',
		weight = 1,
		stack = true,
		close = true,
		description = nil
	},
	['roulette_table'] = {
		label = 'Roulette Table',
		weight = 1,
		stack = true,
		close = true,
		description = nil
	},
	['wheel_machine'] = {
		label = 'Wheel',
		weight = 1,
		stack = true,
		close = true,
		description = nil
	},
	['slot_machine'] = {
		label = 'Slot Machine',
		weight = 1,
		stack = true,
		close = true,
		description = nil
	},
	['horseracing_machine'] = {
		label = 'Horseracing Machine',
		weight = 1,
		stack = true,
		close = true,
		description = nil
	},






	-- QBX Mining System Items
	-- Raw Ores
	['coal_ore'] = {
		label = 'Coal Ore',
		weight = 100,
		stack = true,
		close = true,
		description = 'Raw coal ore that can be smelted into coal',
		client = {
			image = 'coal_ore.png'
		}
	},

	['copper_ore'] = {
		label = 'Copper Ore',
		weight = 150,
		stack = true,
		close = true,
		description = 'Raw copper ore that can be smelted into copper ingots',
		client = {
			image = 'copper_ore.png'
		}
	},

	['iron_ore'] = {
		label = 'Iron Ore',
		weight = 200,
		stack = true,
		close = true,
		description = 'Raw iron ore that can be smelted into iron ingots',
		client = {
			image = 'iron_ore.png'
		}
	},

	['silver_ore'] = {
		label = 'Silver Ore',
		weight = 250,
		stack = true,
		close = true,
		description = 'Raw silver ore that can be smelted into silver ingots',
		client = {
			image = 'silver_ore.png'
		}
	},

	['gold_ore'] = {
		label = 'Gold Ore',
		weight = 300,
		stack = true,
		close = true,
		description = 'Raw gold ore that can be smelted into gold ingots',
		client = {
			image = 'gold_ore.png'
		}
	},

	['diamond_ore'] = {
		label = 'Diamond Ore',
		weight = 50,
		stack = true,
		close = true,
		description = 'Raw diamond ore that can be cut into precious gems',
		client = {
			image = 'diamond_ore.png'
		}
	},

	-- Refined Materials
	['coal'] = {
		label = 'Coal',
		weight = 80,
		stack = true,
		close = true,
		description = 'Refined coal used as fuel for smelting',
		client = {
			image = 'coal.png'
		}
	},

	['copper_ingot'] = {
		label = 'Copper Ingot',
		weight = 120,
		stack = true,
		close = true,
		description = 'Refined copper ingot ready for sale',
		client = {
			image = 'copper_ingot.png'
		}
	},

	['iron_ingot'] = {
		label = 'Iron Ingot',
		weight = 160,
		stack = true,
		close = true,
		description = 'Refined iron ingot ready for sale',
		client = {
			image = 'iron_ingot.png'
		}
	},

	['silver_ingot'] = {
		label = 'Silver Ingot',
		weight = 200,
		stack = true,
		close = true,
		description = 'Refined silver ingot ready for sale',
		client = {
			image = 'silver_ingot.png'
		}
	},

	['gold_ingot'] = {
		label = 'Gold Ingot',
		weight = 240,
		stack = true,
		close = true,
		description = 'Refined gold ingot ready for sale',
		client = {
			image = 'gold_ingot.png'
		}
	},

	['diamond_gem'] = {
		label = 'Cut Diamond',
		weight = 25,
		stack = true,
		close = true,
		description = 'Perfectly cut diamond gem ready for sale',
		client = {
			image = 'diamond_gem.png'
		}
	},

	-- Mining Tools - Pickaxes
	['pickaxe_basic'] = {
		label = 'Basic Pickaxe',
		weight = 1000,
		stack = false,
		close = true,
		description = 'A basic mining pickaxe for beginners',
		client = {
			image = 'pickaxe_basic.png'
		},
		durability = 150
	},

	['pickaxe_iron'] = {
		label = 'Iron Pickaxe',
		weight = 1200,
		stack = false,
		close = true,
		description = 'An improved iron pickaxe with better efficiency',
		client = {
			image = 'pickaxe_iron.png'
		},
		durability = 250
	},

	['pickaxe_steel'] = {
		label = 'Steel Pickaxe',
		weight = 1400,
		stack = false,
		close = true,
		description = 'A durable steel pickaxe for serious miners',
		client = {
			image = 'pickaxe_steel.png'
		},
		durability = 375
	},

	['pickaxe_titanium'] = {
		label = 'Titanium Pickaxe',
		weight = 1600,
		stack = false,
		close = true,
		description = 'A high-tech titanium pickaxe for advanced mining',
		client = {
			image = 'pickaxe_titanium.png'
		},
		durability = 500
	},

	['pickaxe_diamond'] = {
		label = 'Diamond Pickaxe',
		weight = 1800,
		stack = false,
		close = true,
		description = 'The ultimate mining tool with diamond-tipped edges',
		client = {
			image = 'pickaxe_diamond.png'
		},
		durability = 800
	},

	-- Tool Handles
	['handle_wood'] = {
		label = 'Wooden Handle',
		weight = 200,
		stack = false,
		close = true,
		description = 'A basic wooden handle for mining tools',
		client = {
			image = 'handle_wood.png'
		}
	},

	['handle_iron'] = {
		label = 'Iron Handle',
		weight = 300,
		stack = false,
		close = true,
		description = 'An improved iron handle that provides better grip and efficiency',
		client = {
			image = 'handle_iron.png'
		}
	},

	['handle_titanium'] = {
		label = 'Titanium Handle',
		weight = 400,
		stack = false,
		close = true,
		description = 'A lightweight titanium handle with superior performance',
		client = {
			image = 'handle_titanium.png'
		}
	},

	-- QBX Mining System Items - Fresh Implementation
	-- Raw Ores
	['coal_ore'] = {
		label = 'Coal Ore',
		weight = 100,
		stack = true,
		close = true,
		description = 'Raw coal ore that can be smelted into coal',
		client = {
			image = 'coal_ore.png'
		}
	},

	['copper_ore'] = {
		label = 'Copper Ore',
		weight = 150,
		stack = true,
		close = true,
		description = 'Raw copper ore that can be smelted into copper ingots',
		client = {
			image = 'copper_ore.png'
		}
	},

	['iron_ore'] = {
		label = 'Iron Ore',
		weight = 200,
		stack = true,
		close = true,
		description = 'Raw iron ore that can be smelted into iron ingots',
		client = {
			image = 'iron_ore.png'
		}
	},

	['silver_ore'] = {
		label = 'Silver Ore',
		weight = 250,
		stack = true,
		close = true,
		description = 'Raw silver ore that can be smelted into silver ingots',
		client = {
			image = 'silver_ore.png'
		}
	},

	['gold_ore'] = {
		label = 'Gold Ore',
		weight = 300,
		stack = true,
		close = true,
		description = 'Raw gold ore that can be smelted into gold ingots',
		client = {
			image = 'gold_ore.png'
		}
	},

	['diamond_ore'] = {
		label = 'Diamond Ore',
		weight = 50,
		stack = true,
		close = true,
		description = 'Raw diamond ore that can be cut into precious gems',
		client = {
			image = 'diamond_ore.png'
		}
	},

	-- Refined Materials (Updated/New)
	['coal'] = {
		label = 'Coal',
		weight = 80,
		stack = true,
		close = true,
		description = 'Refined coal used as fuel for smelting',
		client = {
			image = 'coal.png'
		}
	},

	['copper_ingot'] = {
		label = 'Copper Ingot',
		weight = 120,
		stack = true,
		close = true,
		description = 'Refined copper ingot ready for sale',
		client = {
			image = 'copper_ingot.png'
		}
	},

	['iron_ingot'] = {
		label = 'Iron Ingot',
		weight = 160,
		stack = true,
		close = true,
		description = 'Refined iron ingot ready for sale',
		client = {
			image = 'iron_ingot.png'
		}
	},

	['silver_ingot'] = {
		label = 'Silver Ingot',
		weight = 200,
		stack = true,
		close = true,
		description = 'Refined silver ingot ready for sale',
		client = {
			image = 'silver_ingot.png'
		}
	},

	['gold_ingot'] = {
		label = 'Gold Ingot',
		weight = 240,
		stack = true,
		close = true,
		description = 'Refined gold ingot ready for sale',
		client = {
			image = 'gold_ingot.png'
		}
	},

	['diamond_gem'] = {
		label = 'Cut Diamond',
		weight = 25,
		stack = true,
		close = true,
		description = 'Perfectly cut diamond gem ready for sale',
		client = {
			image = 'diamond_gem.png'
		}
	},

	-- Mining Tools - Pickaxes (Fresh Implementation)
	['pickaxe_basic'] = {
		label = 'Basic Pickaxe',
		weight = 1000,
		stack = false,
		close = true,
		description = 'A basic mining pickaxe for beginners',
		client = {
			image = 'pickaxe_basic.png'
		},
		durability = 150,
		server = {
			export = 'qbx_mining.usePickaxe'
		}
	},

	['pickaxe_iron'] = {
		label = 'Iron Pickaxe',
		weight = 1200,
		stack = false,
		close = true,
		description = 'An improved iron pickaxe with better efficiency',
		client = {
			image = 'pickaxe_iron.png'
		},
		durability = 250,
		server = {
			export = 'qbx_mining.usePickaxe'
		}
	},

	['pickaxe_steel'] = {
		label = 'Steel Pickaxe',
		weight = 1400,
		stack = false,
		close = true,
		description = 'A durable steel pickaxe for serious miners',
		client = {
			image = 'pickaxe_steel.png'
		},
		durability = 375,
		server = {
			export = 'qbx_mining.usePickaxe'
		}
	},

	['pickaxe_titanium'] = {
		label = 'Titanium Pickaxe',
		weight = 1600,
		stack = false,
		close = true,
		description = 'A high-tech titanium pickaxe for advanced mining',
		client = {
			image = 'pickaxe_titanium.png'
		},
		durability = 500,
		server = {
			export = 'qbx_mining.usePickaxe'
		}
	},

	['pickaxe_diamond'] = {
		label = 'Diamond Pickaxe',
		weight = 1800,
		stack = false,
		close = true,
		description = 'The ultimate mining tool with diamond-tipped edges',
		client = {
			image = 'pickaxe_diamond.png'
		},
		durability = 800,
		server = {
			export = 'qbx_mining.usePickaxe'
		}
	},

	-- Tool Handles (Fresh Implementation)
	['handle_wood'] = {
		label = 'Wooden Handle',
		weight = 200,
		stack = false,
		close = true,
		description = 'A basic wooden handle for mining tools',
		client = {
			image = 'handle_wood.png'
		},
		server = {
			export = 'qbx_mining.useHandle'
		}
	},

	['handle_iron'] = {
		label = 'Iron Handle',
		weight = 300,
		stack = false,
		close = true,
		description = 'An improved iron handle that provides better grip and efficiency',
		client = {
			image = 'handle_iron.png'
		},
		server = {
			export = 'qbx_mining.useHandle'
		}
	},

	['handle_titanium'] = {
		label = 'Titanium Handle',
		weight = 400,
		stack = false,
		close = true,
		description = 'A lightweight titanium handle with superior performance',
		client = {
			image = 'handle_titanium.png'
		},
		server = {
			export = 'qbx_mining.useHandle'
		}
	},

}

