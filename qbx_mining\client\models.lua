-- QBX Mining System - Model and Prop Management Client
local locale = lib.locale

-- Model storage
local LoadedModels = {}
local ActiveProps = {}

-- Pickaxe models configuration
local PickaxeModels = {
    ['pickaxe_basic'] = {
        model = 'prop_tool_pickaxe',
        bone = 57005, -- Right hand bone
        offset = vec3(0.09, -0.53, -0.22),
        rotation = vec3(252.0, 180.0, 0.0)
    },
    ['pickaxe_iron'] = {
        model = 'prop_tool_pickaxe',
        bone = 57005,
        offset = vec3(0.09, -0.53, -0.22),
        rotation = vec3(252.0, 180.0, 0.0)
    },
    ['pickaxe_steel'] = {
        model = 'prop_tool_pickaxe',
        bone = 57005,
        offset = vec3(0.09, -0.53, -0.22),
        rotation = vec3(252.0, 180.0, 0.0)
    },
    ['pickaxe_titanium'] = {
        model = 'prop_tool_pickaxe',
        bone = 57005,
        offset = vec3(0.09, -0.53, -0.22),
        rotation = vec3(252.0, 180.0, 0.0)
    },
    ['pickaxe_diamond'] = {
        model = 'prop_tool_pickaxe',
        bone = 57005,
        offset = vec3(0.09, -0.53, -0.22),
        rotation = vec3(252.0, 180.0, 0.0)
    }
}

-- Node prop models (for visual representation)
local NodeModels = {
    [Types.OreType.COAL] = {
        model = 'prop_rock_4_big2',
        scale = 0.8,
        zOffset = -0.5
    },
    [Types.OreType.COPPER] = {
        model = 'prop_rock_4_big2',
        scale = 0.9,
        zOffset = -0.5
    },
    [Types.OreType.IRON] = {
        model = 'prop_rock_4_big2',
        scale = 1.0,
        zOffset = -0.5
    },
    [Types.OreType.SILVER] = {
        model = 'prop_rock_4_big2',
        scale = 1.1,
        zOffset = -0.5
    },
    [Types.OreType.GOLD] = {
        model = 'prop_rock_4_big2',
        scale = 1.2,
        zOffset = -0.5
    },
    [Types.OreType.DIAMOND] = {
        model = 'prop_rock_4_big2',
        scale = 1.3,
        zOffset = -0.5
    }
}

-- Load a model with timeout
local function LoadModel(modelName, timeout)
    timeout = timeout or 10000
    local modelHash = GetHashKey(modelName)
    
    if HasModelLoaded(modelHash) then
        return true
    end
    
    RequestModel(modelHash)
    
    local startTime = GetGameTimer()
    while not HasModelLoaded(modelHash) and (GetGameTimer() - startTime) < timeout do
        Wait(100)
    end
    
    local success = HasModelLoaded(modelHash)
    
    if success then
        LoadedModels[modelName] = {
            hash = modelHash,
            loadTime = GetGameTimer()
        }
        
        if Config.Debug then
            print('^2[QBX Mining Client]^7 Loaded model: ' .. modelName)
        end
    else
        if Config.Debug then
            print('^1[QBX Mining ERROR]^7 Failed to load model: ' .. modelName)
        end
    end
    
    return success
end

-- Unload a model
local function UnloadModel(modelName)
    local modelHash = GetHashKey(modelName)
    
    if LoadedModels[modelName] then
        SetModelAsNoLongerNeeded(modelHash)
        LoadedModels[modelName] = nil
        
        if Config.Debug then
            print('^2[QBX Mining Client]^7 Unloaded model: ' .. modelName)
        end
    end
end

-- Create a prop at specified location
function CreateProp(modelName, coords, rotation, scale)
    if not LoadModel(modelName) then
        return nil
    end
    
    local modelHash = GetHashKey(modelName)
    local prop = CreateObject(
        modelHash,
        coords.x, coords.y, coords.z,
        false, false, false
    )
    
    if DoesEntityExist(prop) then
        if rotation then
            SetEntityRotation(prop, rotation.x, rotation.y, rotation.z, 2, true)
        end
        
        if scale then
            SetObjectScale(prop, scale)
        end
        
        FreezeEntityPosition(prop, true)
        SetEntityCollision(prop, false, false)
        
        if Config.Debug then
            print('^2[QBX Mining Client]^7 Created prop: ' .. modelName .. ' at ' .. tostring(coords))
        end
        
        return prop
    end
    
    return nil
end

-- Delete a prop
function DeleteProp(prop)
    if DoesEntityExist(prop) then
        DeleteObject(prop)
        return true
    end
    return false
end

-- Attach pickaxe to player
function AttachPickaxeToPlayer(pickaxeType)
    local playerPed = PlayerPedId()
    local pickaxeConfig = PickaxeModels[pickaxeType]
    
    if not pickaxeConfig then
        if Config.Debug then
            print('^1[QBX Mining ERROR]^7 Unknown pickaxe type: ' .. tostring(pickaxeType))
        end
        return nil
    end
    
    -- Remove existing pickaxe
    DetachPickaxeFromPlayer()
    
    -- Load model
    if not LoadModel(pickaxeConfig.model) then
        return nil
    end
    
    -- Create pickaxe prop
    local playerCoords = GetEntityCoords(playerPed)
    local pickaxeProp = CreateProp(pickaxeConfig.model, playerCoords, nil, 1.0)
    
    if not pickaxeProp then
        return nil
    end
    
    -- Attach to player
    AttachEntityToEntity(
        pickaxeProp, playerPed,
        GetPedBoneIndex(playerPed, pickaxeConfig.bone),
        pickaxeConfig.offset.x, pickaxeConfig.offset.y, pickaxeConfig.offset.z,
        pickaxeConfig.rotation.x, pickaxeConfig.rotation.y, pickaxeConfig.rotation.z,
        true, true, false, true, 1, true
    )
    
    -- Store reference
    ActiveProps.pickaxe = {
        entity = pickaxeProp,
        type = pickaxeType,
        attachTime = GetGameTimer()
    }
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Attached pickaxe: ' .. pickaxeType)
    end
    
    return pickaxeProp
end

-- Detach pickaxe from player
function DetachPickaxeFromPlayer()
    if ActiveProps.pickaxe and DoesEntityExist(ActiveProps.pickaxe.entity) then
        DetachEntity(ActiveProps.pickaxe.entity, true, true)
        DeleteProp(ActiveProps.pickaxe.entity)
        
        if Config.Debug then
            print('^2[QBX Mining Client]^7 Detached pickaxe: ' .. ActiveProps.pickaxe.type)
        end
        
        ActiveProps.pickaxe = nil
        return true
    end
    
    return false
end

-- Get currently attached pickaxe
function GetAttachedPickaxe()
    return ActiveProps.pickaxe
end

-- Create node visual props
function CreateNodeProps()
    local nodeProps = {}
    
    for nodeId, node in pairs(MiningNodes) do
        local nodeConfig = NodeModels[node.oreType]
        if nodeConfig then
            local propCoords = vec3(
                node.position.x,
                node.position.y,
                node.position.z + nodeConfig.zOffset
            )
            
            local prop = CreateProp(nodeConfig.model, propCoords, nil, nodeConfig.scale)
            if prop then
                nodeProps[nodeId] = {
                    entity = prop,
                    oreType = node.oreType,
                    nodeId = nodeId
                }
                
                -- Set prop color based on ore type
                SetNodePropColor(prop, node.oreType, node.state)
            end
        end
    end
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Created ' .. table.count(nodeProps) .. ' node props')
    end
    
    return nodeProps
end

-- Set node prop color based on ore type and state
function SetNodePropColor(prop, oreType, state)
    if not DoesEntityExist(prop) then return end
    
    local color = GetNodeColor({oreType = oreType, state = state})
    local alpha = state == Types.NodeState.ACTIVE and 255 or 128
    
    -- Apply color tint (this is a simplified approach)
    SetEntityAlpha(prop, alpha, false)
end

-- Update node prop based on state
function UpdateNodeProp(nodeId, nodeData)
    local nodeProp = ActiveProps.nodes and ActiveProps.nodes[nodeId]
    if nodeProp and DoesEntityExist(nodeProp.entity) then
        SetNodePropColor(nodeProp.entity, nodeData.oreType, nodeData.state)
    end
end

-- Clean up all node props
function CleanupNodeProps()
    if ActiveProps.nodes then
        for nodeId, nodeProp in pairs(ActiveProps.nodes) do
            if DoesEntityExist(nodeProp.entity) then
                DeleteProp(nodeProp.entity)
            end
        end
        ActiveProps.nodes = nil
        
        if Config.Debug then
            print('^2[QBX Mining Client]^7 Cleaned up node props')
        end
    end
end

-- Pickaxe swing animation with prop
function PlayPickaxeSwingAnimation(duration)
    local pickaxe = GetAttachedPickaxe()
    if not pickaxe then return false end
    
    local playerPed = PlayerPedId()
    
    -- Play swing animation
    local animDict = Config.Animations.Dict
    local animName = Config.Animations.Swing
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(
        playerPed, animDict, animName,
        8.0, -8.0, duration or Config.Animations.DurationMs,
        1, 0, false, false, false
    )
    
    return true
end

-- Tool durability visual effects
function ShowToolDurabilityEffect(durabilityPercent)
    local pickaxe = GetAttachedPickaxe()
    if not pickaxe then return end
    
    -- Change pickaxe appearance based on durability
    if durabilityPercent <= 0.1 then
        -- Very low durability - make it look damaged
        SetEntityAlpha(pickaxe.entity, 150, false)
    elseif durabilityPercent <= 0.3 then
        -- Low durability - slight transparency
        SetEntityAlpha(pickaxe.entity, 200, false)
    else
        -- Good durability - full opacity
        SetEntityAlpha(pickaxe.entity, 255, false)
    end
end

-- Preload all mining models
function PreloadMiningModels()
    local modelsToLoad = {}
    
    -- Add pickaxe models
    for _, config in pairs(PickaxeModels) do
        if not table.contains(modelsToLoad, config.model) then
            table.insert(modelsToLoad, config.model)
        end
    end
    
    -- Add node models
    for _, config in pairs(NodeModels) do
        if not table.contains(modelsToLoad, config.model) then
            table.insert(modelsToLoad, config.model)
        end
    end
    
    -- Load all models
    local loadedCount = 0
    for _, modelName in ipairs(modelsToLoad) do
        if LoadModel(modelName) then
            loadedCount = loadedCount + 1
        end
    end
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Preloaded ' .. loadedCount .. '/' .. #modelsToLoad .. ' mining models')
    end
    
    return loadedCount == #modelsToLoad
end

-- Clean up all loaded models
function CleanupAllModels()
    for modelName, _ in pairs(LoadedModels) do
        UnloadModel(modelName)
    end
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Cleaned up all loaded models')
    end
end

-- Clean up all active props
function CleanupAllProps()
    -- Clean up pickaxe
    DetachPickaxeFromPlayer()
    
    -- Clean up node props
    CleanupNodeProps()
    
    -- Clean up any other props
    for propType, propData in pairs(ActiveProps) do
        if propType ~= 'pickaxe' and propType ~= 'nodes' then
            if type(propData) == 'table' and propData.entity then
                if DoesEntityExist(propData.entity) then
                    DeleteProp(propData.entity)
                end
            end
        end
    end
    
    ActiveProps = {}
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Cleaned up all active props')
    end
end

-- Model management thread
function StartModelManagement()
    CreateThread(function()
        while true do
            Wait(300000) -- Check every 5 minutes
            
            -- Clean up old unused models
            local currentTime = GetGameTimer()
            local modelsToUnload = {}
            
            for modelName, modelData in pairs(LoadedModels) do
                -- Unload models that have been loaded for more than 10 minutes and aren't in use
                if (currentTime - modelData.loadTime) > 600000 then
                    local inUse = false
                    
                    -- Check if model is currently attached to player
                    if ActiveProps.pickaxe and PickaxeModels[ActiveProps.pickaxe.type] then
                        if PickaxeModels[ActiveProps.pickaxe.type].model == modelName then
                            inUse = true
                        end
                    end
                    
                    if not inUse then
                        table.insert(modelsToUnload, modelName)
                    end
                end
            end
            
            for _, modelName in ipairs(modelsToUnload) do
                UnloadModel(modelName)
            end
            
            if Config.Debug and #modelsToUnload > 0 then
                print('^2[QBX Mining Client]^7 Unloaded ' .. #modelsToUnload .. ' unused models')
            end
        end
    end)
end

-- Initialize model system
function InitializeModelSystem()
    -- Preload essential models
    PreloadMiningModels()
    
    -- Start model management
    StartModelManagement()
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Model system initialized')
    end
end

-- Event handlers
RegisterNetEvent(Types.Events.SERVER_NODE_UPDATE, function(nodeId, nodeData)
    UpdateNodeProp(nodeId, nodeData)
end)

RegisterNetEvent(Types.Events.SERVER_TOOL_BROKEN, function()
    DetachPickaxeFromPlayer()
end)

-- Exports
exports('AttachPickaxeToPlayer', AttachPickaxeToPlayer)
exports('DetachPickaxeFromPlayer', DetachPickaxeFromPlayer)
exports('GetAttachedPickaxe', GetAttachedPickaxe)
exports('CreateNodeProps', CreateNodeProps)
exports('CleanupNodeProps', CleanupNodeProps)
exports('ShowToolDurabilityEffect', ShowToolDurabilityEffect)
exports('InitializeModelSystem', InitializeModelSystem)
exports('CleanupAllModels', CleanupAllModels)
exports('CleanupAllProps', CleanupAllProps)

-- Auto-initialize
CreateThread(function()
    Wait(1000)
    InitializeModelSystem()
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        CleanupAllProps()
        CleanupAllModels()
    end
end)
