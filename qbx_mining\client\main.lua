-- QBX Mining System - Main Client File
local locale = lib.locale

-- Client Variables
local PlayerData = {}
local MiningNodes = {}
local CurrentMining = {
    active = false,
    nodeId = nil,
    startTime = 0
}
local PlayerLevel = {
    level = 1,
    xp = 0,
    xpToNext = 100
}
local DebugMode = false

-- Initialize client
local function InitializeClient()
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Initializing...')
    end
    
    -- Request initial data from server
    TriggerServerEvent(Types.Events.CLIENT_REQUEST_DATA)
    
    -- Start client threads
    CreateThread(ClientMainThread)
    CreateThread(NodeRenderThread)
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Initialized successfully')
    end
end

-- Main client thread
function ClientMainThread()
    while true do
        local sleep = 1000
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        
        -- Check for nearby nodes
        local nearbyNodes = GetNearbyNodes(playerCoords, Config.Target.Distance)
        
        if #nearbyNodes > 0 then
            sleep = 100
            
            -- Handle node interactions
            for _, node in ipairs(nearbyNodes) do
                if not CurrentMining.active then
                    -- Show interaction prompt
                    DrawText3D(node.position, '[E] Mine ' .. Config.Ores[node.oreType].label)
                    
                    -- Check for E key press
                    if IsControlJustReleased(0, 38) then -- E key
                        local nodeId = GetNearestNodeId(playerCoords)
                        if nodeId then
                            StartMining(nodeId)
                        end
                    end
                end
            end
        end
        
        -- Handle active mining
        if CurrentMining.active then
            sleep = 50
            HandleActiveMining()
        end
        
        Wait(sleep)
    end
end

-- Node rendering thread
function NodeRenderThread()
    while true do
        local sleep = 500
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        
        -- Render nearby nodes
        for nodeId, node in pairs(MiningNodes) do
            local distance = Types.Utils.GetDistance(playerCoords, node.position)
            
            if distance <= 150.0 then -- Render distance
                sleep = 100
                
                -- Draw node marker based on state
                local color = GetNodeColor(node)
                local alpha = node.state == Types.NodeState.ACTIVE and 200 or 100
                
                DrawMarker(
                    1, -- Cylinder
                    node.position.x, node.position.y, node.position.z - 1.0,
                    0.0, 0.0, 0.0,
                    0.0, 0.0, 0.0,
                    1.5, 1.5, 0.5,
                    color.r, color.g, color.b, alpha,
                    false, true, 2, false, nil, nil, false
                )
                
                -- Draw debug info if enabled
                if DebugMode then
                    DrawDebugNodeInfo(node, distance)
                end
            end
        end
        
        Wait(sleep)
    end
end

-- Get node color based on ore type and state
function GetNodeColor(node)
    if node.state ~= Types.NodeState.ACTIVE then
        return { r = 100, g = 100, b = 100 } -- Gray for inactive
    end
    
    local colors = {
        [Types.OreType.COAL] = { r = 50, g = 50, b = 50 },
        [Types.OreType.COPPER] = { r = 184, g = 115, b = 51 },
        [Types.OreType.IRON] = { r = 169, g = 169, b = 169 },
        [Types.OreType.SILVER] = { r = 192, g = 192, b = 192 },
        [Types.OreType.GOLD] = { r = 255, g = 215, b = 0 },
        [Types.OreType.DIAMOND] = { r = 185, g = 242, b = 255 }
    }
    
    return colors[node.oreType] or { r = 255, g = 255, b = 255 }
end

-- Get nearby nodes
function GetNearbyNodes(playerCoords, maxDistance)
    local nearbyNodes = {}
    
    for nodeId, node in pairs(MiningNodes) do
        local distance = Types.Utils.GetDistance(playerCoords, node.position)
        if distance <= maxDistance then
            table.insert(nearbyNodes, node)
        end
    end
    
    return nearbyNodes
end

-- Get nearest node ID to coordinates
function GetNearestNodeId(coords)
    local nearestId = nil
    local nearestDistance = math.huge
    
    for nodeId, node in pairs(MiningNodes) do
        local distance = Types.Utils.GetDistance(coords, node.position)
        if distance < nearestDistance and distance <= Config.Target.Distance then
            nearestDistance = distance
            nearestId = nodeId
        end
    end
    
    return nearestId
end

-- Handle active mining process
function HandleActiveMining()
    local playerPed = PlayerPedId()
    
    -- Check if player is still in range
    local playerCoords = GetEntityCoords(playerPed)
    local node = MiningNodes[CurrentMining.nodeId]
    
    if not node then
        StopMining()
        return
    end
    
    local distance = Types.Utils.GetDistance(playerCoords, node.position)
    if distance > Config.AntiExploit.DistanceCheck then
        StopMining()
        Bridge.Notify('You are too far from the mining node', Types.NotifyType.ERROR)
        return
    end
    
    -- Check if mining animation should continue
    if not IsEntityPlayingAnim(playerPed, Config.Animations.Dict, Config.Animations.Swing, 3) then
        -- Animation stopped, mining complete or interrupted
        StopMining()
    end
end

-- Start mining a node
function StartMining(nodeId)
    local node = MiningNodes[nodeId]
    if not node then return end
    
    if node.state ~= Types.NodeState.ACTIVE then
        Bridge.Notify('This node is not ready for mining', Types.NotifyType.ERROR)
        return
    end
    
    -- Check level requirement
    if PlayerLevel.level < node.minLevel then
        Bridge.Notify('Level ' .. node.minLevel .. ' required to mine ' .. Config.Ores[node.oreType].label, Types.NotifyType.ERROR)
        return
    end
    
    CurrentMining.active = true
    CurrentMining.nodeId = nodeId
    CurrentMining.startTime = GetGameTimer()
    
    -- Play mining animation
    local playerPed = PlayerPedId()
    local animDict = Config.Animations.Dict
    local animName = Config.Animations.Swing
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, Config.Animations.DurationMs, 1, 0, false, false, false)
    
    -- Show progress bar
    local success = Bridge.ProgressBar({
        duration = Config.Animations.DurationMs,
        label = 'Mining in progress...',
        canCancel = true,
        anim = {
            dict = animDict,
            clip = animName
        }
    })
    
    if success then
        -- Send mining request to server
        local playerCoords = GetEntityCoords(playerPed)
        TriggerServerEvent(Types.Events.CLIENT_MINE_NODE, nodeId, playerCoords)
        
        -- Play hit effects
        PlayMiningEffects(node.position)
    end
    
    StopMining()
end

-- Stop mining
function StopMining()
    CurrentMining.active = false
    CurrentMining.nodeId = nil
    CurrentMining.startTime = 0
    
    local playerPed = PlayerPedId()
    ClearPedTasks(playerPed)
end

-- Play mining effects
function PlayMiningEffects(position)
    -- Particle effect
    RequestNamedPtfxAsset(Config.Effects.HitParticle)
    while not HasNamedPtfxAssetLoaded(Config.Effects.HitParticle) do
        Wait(10)
    end
    
    UseParticleFxAssetNextCall(Config.Effects.HitParticle)
    StartParticleFxNonLoopedAtCoord(
        Config.Effects.Asset,
        position.x, position.y, position.z,
        0.0, 0.0, 0.0,
        Config.Effects.Scale,
        false, false, false
    )
    
    -- Sound effect
    PlaySoundFromCoord(-1, Config.Effects.HitSound.name, position.x, position.y, position.z, Config.Effects.HitSound.dict, false, Config.Effects.HitSound.range or 10.0, false)
end

-- Draw 3D text
function DrawText3D(coords, text)
    local onScreen, x, y = World3dToScreen2d(coords.x, coords.y, coords.z + 1.0)
    
    if onScreen then
        local camCoords = GetGameplayCamCoords()
        local distance = Types.Utils.GetDistance(camCoords, coords)
        local scale = (1 / distance) * 2
        local fov = (1 / GetGameplayCamFov()) * 100
        scale = scale * fov * 0.6
        
        SetTextScale(0.0, scale)
        SetTextFont(0)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextDropshadow(0, 0, 0, 0, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(x, y)
    end
end

-- Draw debug node information
function DrawDebugNodeInfo(node, distance)
    local debugText = string.format(
        "ID: %s\nOre: %s\nHealth: %d/%d\nState: %s\nLevel: %d\nDist: %.1fm",
        node.id,
        node.oreType,
        node.health,
        node.maxHealth,
        node.state,
        node.minLevel,
        distance
    )
    
    DrawText3D(vec3(node.position.x, node.position.y, node.position.z + 2.0), debugText)
end

-- Event Handlers
RegisterNetEvent(Types.Events.SERVER_SYNC_NODES, function(nodes)
    MiningNodes = nodes
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Synced ' .. table.count(nodes) .. ' nodes')
    end
end)

RegisterNetEvent(Types.Events.SERVER_NODE_UPDATE, function(nodeId, nodeData)
    if MiningNodes[nodeId] then
        MiningNodes[nodeId] = nodeData
    end
end)

RegisterNetEvent(Types.Events.SERVER_LEVEL_UPDATE, function(levelData)
    PlayerLevel.level = levelData.level
    PlayerLevel.xp = levelData.xp
    PlayerLevel.xpToNext = levelData.xpToNext
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Level updated: ' .. PlayerLevel.level .. ' (XP: ' .. PlayerLevel.xp .. '/' .. PlayerLevel.xpToNext .. ')')
    end
end)

RegisterNetEvent(Types.Events.SERVER_TOOL_BROKEN, function()
    if CurrentMining.active then
        StopMining()
    end
end)

RegisterNetEvent('qbx_mining:client:setDebug', function(enabled)
    DebugMode = enabled
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Debug mode: ' .. (enabled and 'enabled' or 'disabled'))
    end
end)

-- Framework Events - Use QBX events instead of QBCore
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    InitializeClient()
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    PlayerData = {}
    MiningNodes = {}
    StopMining()
end)

-- Exports
exports('StartMining', StartMining)
exports('StopMining', StopMining)
exports('GetPlayerLevel', function() return PlayerLevel end)
exports('GetNearbyNodes', GetNearbyNodes)
exports('IsCurrentlyMining', function() return CurrentMining.active end)

-- Initialize when resource starts
CreateThread(function()
    Wait(1000) -- Wait for framework to load
    InitializeClient()
    
    -- Show UI for testing
    SendNUIMessage({
        action = 'showStats',
        data = {}
    })
end)
