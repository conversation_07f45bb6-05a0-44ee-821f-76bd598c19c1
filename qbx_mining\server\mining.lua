-- QBX Mining System - Mining Logic Server
local locale = lib.locale

-- Node generation using Poisson-disc sampling for even distribution
local function GeneratePoissonNodes(center, radius, minDistance, count, zBand)
    local nodes = {}
    local attempts = 0
    local maxAttempts = count * 50
    
    -- Generate first node at center with random offset
    local firstNode = {
        x = center.x + math.random(-radius/4, radius/4),
        y = center.y + math.random(-radius/4, radius/4),
        z = math.random(zBand[2], zBand[1])
    }
    table.insert(nodes, firstNode)
    
    while #nodes < count and attempts < maxAttempts do
        attempts = attempts + 1
        
        -- Generate random point within radius
        local angle = math.random() * 2 * math.pi
        local distance = math.sqrt(math.random()) * radius
        
        local candidate = {
            x = center.x + math.cos(angle) * distance,
            y = center.y + math.sin(angle) * distance,
            z = math.random(zBand[2], zBand[1])
        }
        
        -- Check minimum distance to existing nodes
        local validPosition = true
        for _, existingNode in ipairs(nodes) do
            local dist = math.sqrt(
                (candidate.x - existingNode.x)^2 + 
                (candidate.y - existingNode.y)^2
            )
            if dist < minDistance then
                validPosition = false
                break
            end
        end
        
        if validPosition then
            table.insert(nodes, candidate)
        end
    end
    
    return nodes
end

-- Generate all mining nodes
function GenerateNodes()
    MiningData.nodes = {}
    local totalNodes = 0
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Generating mining nodes...')
    end
    
    for _, layer in ipairs(Config.Quarry.Levels) do
        local minDistance = layer.radius / math.sqrt(layer.count) * 0.8
        local positions = GeneratePoissonNodes(
            Config.Quarry.Center,
            layer.radius,
            minDistance,
            layer.count,
            layer.zBand
        )
        
        for _, pos in ipairs(positions) do
            for _, oreType in ipairs(layer.ores) do
                local nodeId = Types.Utils.GenerateNodeId()
                
                MiningData.nodes[nodeId] = {
                    id = nodeId,
                    position = vec3(pos.x, pos.y, pos.z),
                    oreType = oreType,
                    health = Config.Quarry.NodeHealth[oreType] or Types.Constants.DEFAULT_NODE_HEALTH,
                    maxHealth = Config.Quarry.NodeHealth[oreType] or Types.Constants.DEFAULT_NODE_HEALTH,
                    state = Types.NodeState.ACTIVE,
                    minLevel = layer.minLevel,
                    respawnTime = 0,
                    lastHit = 0
                }
                
                totalNodes = totalNodes + 1
            end
        end
    end
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Generated ' .. totalNodes .. ' mining nodes across ' .. #Config.Quarry.Levels .. ' layers')
    end
end

-- Validate mining attempt
function ValidateMining(src, nodeId, playerPos)
    local node = MiningData.nodes[nodeId]
    local player = MiningData.players[src]
    
    if not node then
        return { success = false, error = 'invalid_node' }
    end
    
    if not player then
        return { success = false, error = 'server_error' }
    end
    
    -- Check if node is active
    if node.state ~= Types.NodeState.ACTIVE then
        return { success = false, error = 'node_not_ready' }
    end
    
    -- Check distance
    local distance = Types.Utils.GetDistance(playerPos, node.position)
    if distance > Config.AntiExploit.DistanceCheck then
        return { success = false, error = 'too_far' }
    end
    
    -- Check line of sight (basic implementation)
    if Config.AntiExploit.LosCheck then
        local hit = RaycastGameplayCamera(1000.0)
        if hit ~= 1 then
            return { success = false, error = 'no_line_of_sight' }
        end
    end
    
    -- Check rate limiting
    local currentTime = GetGameTimer()
    if (currentTime - player.lastHit) < (Config.AntiExploit.ServerHitRateLimit * 1000) then
        return { success = false, error = 'rate_limited' }
    end
    
    -- Check level requirement
    if player.level < node.minLevel then
        return { success = false, error = 'level_required' }
    end
    
    -- Check if player has a pickaxe
    local pickaxe = GetPlayerPickaxe(src)
    if not pickaxe then
        return { success = false, error = 'tool_required' }
    end
    
    return { success = true, node = node, pickaxe = pickaxe }
end

-- Get player's current pickaxe
function GetPlayerPickaxe(src)
    local inventory = exports.ox_inventory:GetInventory(src)
    if not inventory then return nil end
    
    -- Check for any pickaxe in inventory
    for _, pickaxeData in ipairs(Config.Tools.Pickaxes) do
        local item = exports.ox_inventory:GetItem(src, pickaxeData.item, nil, false)
        if item and item.count > 0 then
            -- Check durability
            local durability = item.metadata and item.metadata.durability or pickaxeData.durability
            if durability > 0 then
                return {
                    item = pickaxeData.item,
                    data = pickaxeData,
                    durability = durability,
                    slot = item.slot
                }
            end
        end
    end
    
    return nil
end

-- Get player's current handle
function GetPlayerHandle(src)
    local inventory = exports.ox_inventory:GetInventory(src)
    if not inventory then return nil end
    
    -- Check for any handle in inventory
    for _, handleData in ipairs(Config.Tools.Handles) do
        local item = exports.ox_inventory:GetItem(src, handleData.item, nil, false)
        if item and item.count > 0 then
            return {
                item = handleData.item,
                data = handleData
            }
        end
    end
    
    return nil
end

-- Process mining attempt
function ProcessMining(src, nodeId, node)
    local player = MiningData.players[src]
    local pickaxe = GetPlayerPickaxe(src)
    local handle = GetPlayerHandle(src)
    
    if not pickaxe then
        Bridge.Notify(src, locale('tool_required'), Types.NotifyType.ERROR)
        return
    end
    
    -- Calculate damage based on tool stats
    local baseDamage = pickaxe.data.baseDamage
    local speedMultiplier = pickaxe.data.speedMul
    local rareBoost = pickaxe.data.rareBoost
    
    if handle then
        speedMultiplier = speedMultiplier * handle.data.speedMul
        rareBoost = rareBoost + handle.data.doubleDrop
    end
    
    -- Apply damage to node
    node.health = node.health - baseDamage
    node.lastHit = GetGameTimer()
    player.lastHit = GetGameTimer()
    
    -- Reduce pickaxe durability
    local newDurability = pickaxe.durability - 1
    if newDurability <= 0 then
        -- Tool breaks
        exports.ox_inventory:RemoveItem(src, pickaxe.item, 1, nil, pickaxe.slot)
        Bridge.Notify(src, locale('tool_broken', pickaxe.data.label), Types.NotifyType.WARNING)
        TriggerClientEvent(Types.Events.SERVER_TOOL_BROKEN, src)
        return
    else
        -- Update durability
        exports.ox_inventory:SetDurability(src, pickaxe.slot, newDurability)
    end
    
    -- Check if node is depleted
    if node.health <= 0 then
        -- Node depleted, give rewards
        local oreType = node.oreType
        local baseAmount = 1
        
        -- Calculate bonus drops
        local bonusChance = rareBoost / 100
        local bonusAmount = 0
        
        if math.random() < bonusChance then
            bonusAmount = math.random(1, 2)
            Bridge.Notify(src, locale('rare_find'), Types.NotifyType.SUCCESS)
        end
        
        -- Handle double drop chance
        if handle and math.random() < (handle.data.doubleDrop / 100) then
            bonusAmount = bonusAmount + baseAmount
        end
        
        local totalAmount = baseAmount + bonusAmount
        
        -- Give ore to player
        local oreItem = Config.Ores[oreType].item
        local success = exports.ox_inventory:AddItem(src, oreItem, totalAmount)
        
        if success then
            Bridge.Notify(src, locale('mining_success', totalAmount, Config.Ores[oreType].label), Types.NotifyType.SUCCESS)
            
            -- Add XP
            local xpGain = Config.Progression.XPPerOre[oreType] or 10
            AddXP(src, xpGain)
            
            -- Set node to respawning
            node.state = Types.NodeState.DEPLETED
            local respawnTime = Config.Quarry.Respawn[oreType] or Config.Quarry.NodeRespawnSeconds
            node.respawnTime = GetGameTimer() + (respawnTime * 1000)
            
            -- Schedule respawn
            SetTimeout(respawnTime * 1000, function()
                node.health = node.maxHealth
                node.state = Types.NodeState.ACTIVE
                node.respawnTime = 0
                
                -- Notify nearby players
                TriggerClientEvent(Types.Events.SERVER_NODE_UPDATE, -1, nodeId, node)
            end)
            
        else
            Bridge.Notify(src, locale('inventory_full'), Types.NotifyType.ERROR)
            -- Restore node health if inventory full
            node.health = node.health + baseDamage
        end
    end
    
    -- Update node for all clients
    TriggerClientEvent(Types.Events.SERVER_NODE_UPDATE, -1, nodeId, node)
end

-- Calculate XP required for next level
function CalculateXPToNext(level)
    if level >= Config.Progression.MaxLevel then
        return 0
    end
    
    local formula = Config.Progression.XPFormula
    return math.floor(formula.Base * (level ^ formula.Exponent) + formula.Linear * level)
end

-- Add XP to player
function AddXP(src, amount)
    local player = MiningData.players[src]
    if not player then return false end
    
    if player.level >= Config.Progression.MaxLevel then
        Bridge.Notify(src, locale('max_level_reached'), Types.NotifyType.INFO)
        return false
    end
    
    player.xp = player.xp + amount
    Bridge.Notify(src, locale('xp_gained', amount), Types.NotifyType.SUCCESS)
    
    -- Check for level up
    local xpRequired = CalculateXPToNext(player.level)
    while player.xp >= xpRequired and player.level < Config.Progression.MaxLevel do
        player.level = player.level + 1
        player.xp = player.xp - xpRequired
        
        Bridge.Notify(src, locale('level_up', player.level), Types.NotifyType.SUCCESS)
        
        -- Save level to metadata
        Bridge.SetMetadata(src, Config.Progression.MetadataKeys.level, player.level)
        
        if player.level < Config.Progression.MaxLevel then
            xpRequired = CalculateXPToNext(player.level)
        else
            player.xp = 0
            break
        end
    end
    
    -- Save XP to metadata
    Bridge.SetMetadata(src, Config.Progression.MetadataKeys.xp, player.xp)
    
    -- Update client
    TriggerClientEvent(Types.Events.SERVER_LEVEL_UPDATE, src, {
        level = player.level,
        xp = player.xp,
        xpToNext = player.level < Config.Progression.MaxLevel and CalculateXPToNext(player.level) or 0
    })
    
    return true
end

-- Node respawn check (called periodically)
function CheckNodeRespawns()
    local currentTime = GetGameTimer()
    local respawnedNodes = {}
    
    for nodeId, node in pairs(MiningData.nodes) do
        if node.state == Types.NodeState.DEPLETED and node.respawnTime > 0 and currentTime >= node.respawnTime then
            node.health = node.maxHealth
            node.state = Types.NodeState.ACTIVE
            node.respawnTime = 0
            
            table.insert(respawnedNodes, nodeId)
        end
    end
    
    -- Notify clients of respawned nodes
    if #respawnedNodes > 0 then
        for _, nodeId in ipairs(respawnedNodes) do
            TriggerClientEvent(Types.Events.SERVER_NODE_UPDATE, -1, nodeId, MiningData.nodes[nodeId])
        end
    end
end

-- Start node respawn checker
CreateThread(function()
    while true do
        Wait(5000) -- Check every 5 seconds
        CheckNodeRespawns()
    end
end)

-- Exports
exports('GenerateNodes', GenerateNodes)
exports('ValidateMining', ValidateMining)
exports('ProcessMining', ProcessMining)
exports('AddXP', AddXP)
exports('CalculateXPToNext', CalculateXPToNext)
exports('GetPlayerPickaxe', GetPlayerPickaxe)
exports('GetPlayerHandle', GetPlayerHandle)
