<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QBX Mining System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="mining-ui" class="mining-container" style="display: none;">
        <!-- Mining Progress Display -->
        <div id="mining-progress" class="progress-container">
            <div class="progress-header">
                <h3 id="progress-title">Mining in Progress...</h3>
                <div class="progress-info">
                    <span id="progress-ore">Coal Ore</span>
                    <span id="progress-level">Level 1</span>
                </div>
            </div>
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
            </div>
            <div class="progress-stats">
                <span id="progress-xp">XP: 0/100</span>
                <span id="progress-time">Time: 0s</span>
            </div>
        </div>

        <!-- Smelting Progress Display -->
        <div id="smelting-progress" class="smelting-container">
            <div class="smelting-header">
                <h3>Smelting Queue</h3>
                <button id="close-smelting" class="close-btn">&times;</button>
            </div>
            <div id="smelting-jobs" class="smelting-jobs">
                <!-- Smelting jobs will be populated here -->
            </div>
        </div>

        <!-- Level Up Notification -->
        <div id="level-up" class="level-up-notification">
            <div class="level-up-content">
                <h2>LEVEL UP!</h2>
                <div class="level-display">
                    <span id="new-level">2</span>
                </div>
                <p>Mining Level Increased!</p>
            </div>
        </div>

        <!-- Mining Statistics -->
        <div id="mining-stats" class="stats-container">
            <div class="stats-header">
                <h3>Mining Statistics</h3>
                <button id="toggle-stats" class="toggle-btn">Hide</button>
            </div>
            <div class="stats-content">
                <div class="stat-item">
                    <span class="stat-label">Current Level:</span>
                    <span id="stat-level" class="stat-value">1</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Total XP:</span>
                    <span id="stat-xp" class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Ores Mined:</span>
                    <span id="stat-ores" class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Session Time:</span>
                    <span id="stat-time" class="stat-value">0m</span>
                </div>
            </div>
        </div>

        <!-- Tool Durability Display -->
        <div id="tool-durability" class="durability-container">
            <div class="durability-header">
                <span id="tool-name">Basic Pickaxe</span>
                <span id="durability-percent">100%</span>
            </div>
            <div class="durability-bar">
                <div id="durability-fill" class="durability-fill"></div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
