Config = {
  Debug = false,

  Framework = 'qbox',
  Locale = 'en',

  Quarry = {
    -- Node layout generation
    Seed = 1337,
    NodeRespawnSeconds = 300,       -- global default; can be per-ore override
    EvenDistribution = true,        -- Poisson-disc-like spacing
    MaxActiveNodes = 180,           -- perf cap
    Levels = {
      -- Layers from top (easy) to bottom (hard); minLevel gates access
      { name='Coal Layer',    minLevel=1,   ores={'coal'},    zBand={43.0, 42.0},  radius=75.0, count=45 },
      { name='Copper Layer',  minLevel=10,  ores={'copper'},  zBand={42.0, 40.5},  radius=65.0, count=40 },
      { name='Iron Layer',    minLevel=20,  ores={'iron'},    zBand={40.5, 39.0},  radius=55.0, count=35 },
      { name='Silver Layer',  minLevel=30,  ores={'silver'},  zBand={39.0, 37.0},  radius=45.0, count=30 },
      { name='Gold Layer',    minLevel=40,  ores={'gold'},    zBand={37.0, 35.5},  radius=35.0, count=20 },
      { name='Diamond Layer', minLevel=50,  ores={'diamond'}, zBand={35.5, 34.5},  radius=25.0, count=10 },
    },
    Center = vec3(2950.0, 2750.0, 43.2), -- approx quarry center for radial placement
    NodeHealth = {                       -- hits to break with Basic pickaxe baseline
      coal=3, copper=5, iron=7, silver=9, gold=11, diamond=14
    },
    Respawn = {                          -- per-ore override; fallback to NodeRespawnSeconds
      coal=120, copper=180, iron=240, silver=300, gold=360, diamond=480
    },
  },

  Ores = {
    coal    = { label='Coal Ore',    item='coal_ore' },
    copper  = { label='Copper Ore',  item='copper_ore' },
    iron    = { label='Iron Ore',    item='iron_ore' },
    silver  = { label='Silver Ore',  item='silver_ore' },
    gold    = { label='Gold Ore',    item='gold_ore' },
    diamond = { label='Diamond Ore', item='diamond_ore' },
  },

  Ingots = {
    coal    = { label='Coal',         item='coal' }, -- used as fuel & sellable
    copper  = { label='Copper Ingot', item='copper_ingot' },
    iron    = { label='Iron Ingot',   item='iron_ingot' },
    silver  = { label='Silver Ingot', item='silver_ingot' },
    gold    = { label='Gold Ingot',   item='gold_ingot' },
    diamond = { label='Cut Diamond',  item='diamond_gem' },
  },

  Tools = {
    -- Each pickaxe has model, anim speed multiplier, baseDamage (to node HP),
    -- rareDropBoost (%), and durability (uses server-side decrement).
    Pickaxes = {
      { item='pickaxe_basic',    label='Basic Pickaxe',    tier=1, model='prop_tool_pickaxe',  baseDamage=1,  speedMul=1.00, rareBoost=0,   durability=150, minLevel=1,  price=500 },
      { item='pickaxe_iron',     label='Iron Pickaxe',     tier=2, model='prop_tool_pickaxe2', baseDamage=2,  speedMul=1.05, rareBoost=2,   durability=250, minLevel=10, price=2500 },
      { item='pickaxe_steel',    label='Steel Pickaxe',    tier=3, model='prop_tool_pickaxe3', baseDamage=3,  speedMul=1.10, rareBoost=4,   durability=375, minLevel=20, price=6000 },
      { item='pickaxe_titanium', label='Titanium Pickaxe', tier=4, model='prop_tool_pickaxe4', baseDamage=4,  speedMul=1.15, rareBoost=7,   durability=500, minLevel=35, price=12000 },
      { item='pickaxe_diamond',  label='Diamond Pickaxe',  tier=5, model='prop_tool_pickaxe5', baseDamage=6,  speedMul=1.25, rareBoost=12,  durability=800, minLevel=50, price=25000 },
    },
    Handles = {
      -- Stacks multiplicatively with pickaxe stats server-side.
      { item='handle_wood',     label='Wooden Handle',     speedMul=1.00, staminaRegen=0.00, doubleDrop=0,   durabBonus=0,   price=250,  minLevel=1 },
      { item='handle_iron',     label='Iron Handle',       speedMul=1.05, staminaRegen=0.05, doubleDrop=3,   durabBonus=50,  price=1500, minLevel=15 },
      { item='handle_titanium', label='Titanium Handle',   speedMul=1.10, staminaRegen=0.10, doubleDrop=6,   durabBonus=120, price=6000, minLevel=30 },
    }
  },

  Progression = {
    MaxLevel = 50,
    -- XP formula: xpToNext(level) = floor(Base * (level^Exponent)) + Linear*level
    XPFormula = { Base=100, Exponent=1.35, Linear=25 },
    XPPerOre = { coal=15, copper=25, iron=35, silver=50, gold=70, diamond=100 },
    LevelGates = { coal=1, copper=10, iron=20, silver=30, gold=40, diamond=50 },
    MetadataKeys = { xp='mining_xp', level='mining_level', handle='mining_handle', pickaxe='mining_pickaxe' },
  },

  Smelting = {
    Ped = { coords = vec4(1109.9565, -2008.1145, 31.0561, 246.1676) },
    RequiresCoal = true,
    CoalPerBatch = function(amount) return math.ceil(amount / 25) end, -- e.g., 1 coal per 25 ore
    MaxBatch = 250,
    TimePerOreSeconds = 1.2,       -- 1–5 min total depending on qty; tune with cap below
    MinSeconds = 60,
    MaxSeconds = 300,
    QueuePersist = true,           -- survive restarts (server persistence)
  },

  Selling = {
    Ped = { coords = vec4(1091.0190, -1998.3516, 31.1318, 145.6556) },
    Prices = {                     -- per ingot/gem (server-side authoritative)
      coal=3, copper_ingot=25, iron_ingot=45, silver_ingot=80, gold_ingot=130, diamond_gem=600
    },
    TaxRate = 0.0,                 -- optional cut
    PayTo = 'bank',                -- 'cash' or 'bank'
    PriceFluctuation = { Enabled=false, Min=0.9, Max=1.2, PeriodMinutes=60 },
  },

  Vendor = {
    Ped = { coords = vec4(2944.5332, 2746.8093, 43.3676, 291.3429) },
    Categories = { 'Pickaxes', 'Handles', 'Supplies' },
    Supplies = {
      { item='bandage',     label='Bandage',      price=50 },
      { item='water',       label='Water Bottle', price=10 },
      { item='sandwich',    label='Sandwich',     price=25 },
      { item='coal',        label='Coal',         price=12 }, -- optional direct fuel purchase
    }
  },

  Target = {
    PedIcon = 'fa-person-digging',
    NodeIcon = 'fa-gem',
    Distance = 2.0,
  },

  Animations = {
    Dict = 'melee@large_wpn@streamed_core',
    Swing = 'ground_attack_on_spot',
    DurationMs = 1600,
    UseRagdollFailChance = 0.0
  },

  Effects = {
    HitParticle = 'core', Asset='ent_dst_rocks', Scale=1.2,
    HitSound = { dict='DLC_HEIST_BIOLAB_PREP_HACKING_SOUNDS', name='Hack_Success' }
  },

  AntiExploit = {
    EntitySpamCooldownMs = 1500,
    ServerHitRateLimit = 1.0, -- sec between valid hits per player
    DistanceCheck = 4.0,      -- max distance from node when hitting
    LosCheck = true,
    HashWhitelistNodes = true
  }
}
