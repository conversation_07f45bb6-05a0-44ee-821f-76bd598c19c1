body {
	margin: 0;
	font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Lexend, Oxygen, Ubuntu, Cantarell, Fira <PERSON>s, Droid Sans, Helvetica Neue, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	height: 100vh;
	background: none !important;
	overflow: hidden !important;
	user-select: none
}

#root {
	height: 100%
}

code {
	font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace
}

::-webkit-scrollbar {
	display: none
}

p {
	margin: 0;
	padding: 0;
	font-family: Lexend
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none
}

.app-wrapper {
	height: 100%;
	width: 100%;
	color: #fff
}

.context-menu-list {
	min-width: 200px;
	background-color: #22232c;
	color: #c1c2c5;
	padding: 4px;
	border-color: #0003;
	border-style: inset;
	border-width: 1px;
	border-radius: 4px;
	outline: none;
	display: flex;
	flex-direction: column
}

.context-menu-item {
	padding: 8px;
	border-radius: 4px;
	background-color: transparent;
	outline: none;
	border: none;
	color: #c1c2c5;
	display: flex;
	justify-content: space-between;
	align-items: center
}

.context-menu-item:active {
	transform: none
}

.context-menu-item:hover {
	background-color: #33343f;
	cursor: pointer
}

.tooltip-description {
	padding-top: 5px
}

.tooltip-markdown>p {
	margin: 0
}

button:active {
	transform: translateY(3px)
}

.item-drag-preview {
	width: 7.7vh;
	height: 7.7vh;
	z-index: 1;
	position: fixed;
	pointer-events: none;
	top: 0;
	left: 0;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 7vh;
	image-rendering: -webkit-optimize-contrast
}

.inventory-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: flex-start;
	height: 100%;
	gap: 20px;
	padding-top: 50px
}

.inventory-control { 
	display: flex 
}

.inventory-control .inventory-control-wrapper {
	display: flex;
	flex-direction: column;
	gap: 20px;
	justify-content: center;
	align-items: center
}

.inventory-control .InventoryLogo {
	position: relative !important;
	top: -20px !important;
	margin-bottom: -20px !important
}

.inventory-control .inventory-control-input {
	transition: .2s;
	padding: 16px 8px;
	border-radius: 2.5%;
	font-family: Lexend;
	font-size: 16px;
	text-align: center;
	outline: none;
	border: none;
	color: #fff;
	background-color: #0c0c0c66
}

.inventory-control .inventory-control-input:focus-within {
	background-color: #0c0c0ccc
}

.inventory-control .inventory-control-button {
	font-size: 14px;
	color: #fff;
	background-color: #0c0c0c66;
	transition: .2s;
	padding: 12px 8px;
	border-radius: 2.5%;
	border: none;
	text-transform: uppercase;
	font-family: Lexend;
	width: 100%;
	font-weight: 500
}

.inventory-control .inventory-control-button:hover {
	background-color: #0c0c0ccc
}

.inventory-control .InventoryLogo {
	position: relative !important;
	top: -20px !important;
	margin-bottom: -20px !important
}

.useful-controls-dialog {
	background-color: #22232c;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #c1c2c5;
	width: 450px;
	display: flex;
	flex-direction: column;
	padding: 16px;
	border-radius: 4px;
	gap: 16px
}

.useful-controls-dialog-overlay {
	background-color: #00000080
}

.useful-controls-dialog-title {
	display: flex;
	width: 100%;
	justify-content: space-between;
	align-items: center;
	font-size: 18px
}

.useful-controls-dialog-close {
	width: 25px;
	height: 25px;
	padding: 6px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 4px;
	fill: #c1c2c5
}

.useful-controls-dialog-close:hover {
	background-color: #33343f;
	cursor: pointer
}

.useful-controls-content-wrapper {
	display: flex;
	flex-direction: column;
	gap: 20px
}

.divider {
	width: 100%;
	height: 1px;
	background-color: #ffffff1f
}

.useful-controls-button {
	position: absolute !important;
	bottom: 25px;
	right: 25px;
	transition: .2s !important;
	border: none;
	color: #fff;
	width: 52px;
	height: 52px;
	display: flex;
	justify-content: center;
	align-items: center;
	fill: #fff;
	border-radius: 5% !important;
	background-color: #0c0c0c66 !important
}

.useful-controls-button:hover {
	background-color: #0c0c0ccc !important;
	cursor: pointer
}

.useful-controls-exit-button {
	position: absolute !important;
	right: 8px;
	top: 8px;
	border-radius: 2.5% !important;
	color: gray !important
}

.inventory-grid-wrapper {
	display: flex;
	flex-direction: column;
	gap: 4px
}

.inventory-grid-header-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: space-between
}

.inventory-grid-header-wrapper p {
	font-size: 16px
}

.inventory-grid-container {
	display: grid;
	height: calc(52.1vh + 10px);
	grid-template-columns: repeat(5, 10.2vh);
	grid-auto-rows: 10.42vh;
	gap: 2px;
	overflow-y: scroll
}

.inventory-slot,
.item-notification-item-box,
.hotbar-item-slot {
	background-color: #0c0c0c66;
	background-repeat: no-repeat;
	background-position: center;
	border-radius: 2.5%;
	image-rendering: -webkit-optimize-contrast;
	position: relative;
	background-size: 7vh;
	color: #c1c2c5;
	border-color: #0003;
	border-style: inset;
	border-width: 1px
}

.inventory-slot-label-box {
	background-color: #22232c;
	color: #c1c2c5;
	text-align: center;
	border-bottom-left-radius: .25vh;
	border-bottom-right-radius: .25vh;
	border-top-color: #0003;
	border-top-style: inset;
	border-top-width: 1px
}

.inventory-slot-label-text {
	text-transform: uppercase;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: 2px 3px;
	font-weight: 400;
	font-family: Lexend;
	font-size: 12px
}

.inventory-slot-number {
	background-color: #fff;
	color: #000;
	height: 12px;
	border-top-left-radius: .25vh;
	border-bottom-right-radius: .25vh;
	padding: 3px;
	font-size: 12px;
	font-family: Lexend
}

.item-slot-wrapper {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%
}

.item-slot-wrapper p {
	font-size: 12px
}

.item-slot-header-wrapper,
.item-hotslot-header-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: flex-end
}

.item-hotslot-header-wrapper {
	justify-content: space-between !important
}

.item-slot-info-wrapper {
	display: flex;
	flex-direction: row;
	align-self: flex-end;
	padding: 3px;
	gap: 3px
}

.item-slot-info-wrapper p {
	font-size: 12px
}

.item-slot-currency-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	align-items: center;
	padding-right: 3px
}

.item-slot-currency-wrapper p {
	font-size: 14px;
	text-shadow: .1vh .1vh 0 rgba(0, 0, 0, .7)
}

.item-slot-price-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	padding-right: 3px
}

.item-slot-price-wrapper p {
	font-size: 14px;
	text-shadow: .1vh .1vh 0 rgba(0, 0, 0, .7)
}

.tooltip-wrapper {
	pointer-events: none;
	display: flex;
	background-color: #22232c;
	width: 200px;
	padding: 8px;
	flex-direction: column;
	min-width: 200px;
	color: #c1c2c5;
	font-family: Lexend;
	border-radius: 4px;
	border-color: #0003;
	border-style: inset;
	border-width: 1px
}

.tooltip-wrapper p {
	font-size: 12px;
	font-weight: 400
}

.tooltip-header-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center
}

.tooltip-header-wrapper p {
	font-size: 15px;
	font-weight: 400
}

.tooltip-crafting-duration {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center
}

.tooltip-crafting-duration svg {
	padding-right: 3px
}

.tooltip-crafting-duration p {
	font-size: 14px
}

.tooltip-ingredients {
	padding-top: 5px
}

.tooltip-ingredient {
	display: flex;
	flex-direction: row;
	align-items: center
}

.tooltip-ingredient img {
	width: 28px;
	height: 28px;
	padding-right: 5px
}

.hotbar-container {
	display: flex;
	align-items: center;
	gap: 2px;
	justify-content: center;
	width: 100%;
	position: absolute;
	bottom: 2vh
}

.hotbar-item-slot {
	width: 10.2vh;
	height: 10.2vh
}

.hotbar-slot-header-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: space-between
}

.item-notification-container {
	display: flex;
	overflow-x: scroll;
	flex-wrap: nowrap;
	gap: 2px;
	position: absolute;
	bottom: 20vh;
	left: 50%;
	width: 100%;
	margin-left: calc(50% - 5.1vh);
	transform: translate(-50%)
}

.item-notification-action-box {
	width: 100%;
	color: #c1c2c5;
	background-color: #0c0c0c66;
	text-transform: uppercase;
	text-align: center;
	border-top-left-radius: .25vh;
	border-top-right-radius: .25vh;
	font-family: Lexend
}

.item-notification-action-box p {
	font-size: 11px;
	padding: 2px;
	font-weight: 600
}

.item-notification-item-box {
	height: 10.2vh;
	width: 10.2vh
}

.durability-bar {
	background: rgba(0, 0, 0, .5);
	height: 3px;
	overflow: hidden
}

.weight-bar {
	background: rgba(0, 0, 0, .4);
	border: 1px inset rgba(0, 0, 0, .1);
	height: .8vh;
	border-radius: 5%;
	overflow: hidden
}

.transition-fade-enter {
	opacity: 0
}

.transition-fade-enter-active {
	opacity: 1;
	transition: opacity .2s
}

.transition-fade-exit {
	opacity: 1
}

.transition-fade-exit-active {
	opacity: 0;
	transition: opacity .2s
}

.transition-slide-up-enter {
	transform: translateY(200px)
}

.transition-slide-up-enter-active {
	transform: translateY(0);
	transition: all .2s
}

.transition-slide-up-exit {
	transform: translateY(0)
}

.transition-slide-up-exit-active {
	transform: translateY(200px);
	transition: all .2s
}