-- QBX Mining System - Ped Management Client
local locale = lib.locale

-- Ped storage
local MiningPeds = {
    vendor = nil,
    smelter = nil,
    seller = nil
}

-- Ped configurations
local PedConfigs = {
    vendor = {
        model = 'mp_m_waremech_01',
        coords = Config.Vendor.Ped.coords,
        scenario = 'WORLD_HUMAN_CLIPBOARD',
        targetOptions = {
            {
                name = 'mining_vendor',
                icon = Config.Target.PedIcon,
                label = locale('open_vendor'),
                onSelect = function()
                    OpenVendorMenu()
                end
            }
        }
    },
    smelter = {
        model = 'mp_m_weapexp_01',
        coords = Config.Smelting.Ped.coords,
        scenario = 'WORLD_HUMAN_WELDING',
        targetOptions = {
            {
                name = 'mining_smelter',
                icon = 'fas fa-fire',
                label = locale('open_smelter'),
                onSelect = function()
                    OpenSmelterMenu()
                end
            }
        }
    },
    seller = {
        model = 'mp_m_shopkeep_01',
        coords = Config.Selling.Ped.coords,
        scenario = 'WORLD_HUMAN_STAND_IMPATIENT',
        targetOptions = {
            {
                name = 'mining_seller',
                icon = 'fas fa-coins',
                label = locale('open_seller'),
                onSelect = function()
                    OpenSellerMenu()
                end
            }
        }
    }
}

-- Spawn a ped with configuration
local function SpawnPed(pedType, config)
    if MiningPeds[pedType] then
        DeletePed(MiningPeds[pedType])
        MiningPeds[pedType] = nil
    end
    
    -- Load model
    local modelHash = GetHashKey(config.model)
    RequestModel(modelHash)
    
    local timeout = 0
    while not HasModelLoaded(modelHash) and timeout < 10000 do
        Wait(100)
        timeout = timeout + 100
    end
    
    if not HasModelLoaded(modelHash) then
        if Config.Debug then
            print('^1[QBX Mining ERROR]^7 Failed to load ped model: ' .. config.model)
        end
        return false
    end
    
    -- Create ped
    local ped = CreatePed(
        4, -- PED_TYPE_CIVMALE
        modelHash,
        config.coords.x,
        config.coords.y,
        config.coords.z - 1.0,
        config.coords.w,
        false,
        true
    )
    
    if not DoesEntityExist(ped) then
        if Config.Debug then
            print('^1[QBX Mining ERROR]^7 Failed to create ped: ' .. pedType)
        end
        return false
    end
    
    -- Configure ped
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    FreezeEntityPosition(ped, true)
    SetEntityCanBeDamaged(ped, false)
    SetPedCanRagdollFromPlayerImpact(ped, false)
    SetPedConfigFlag(ped, 32, true) -- CPED_CONFIG_FLAG_IsFemale
    
    -- Set scenario
    if config.scenario then
        TaskStartScenarioInPlace(ped, config.scenario, 0, true)
    end
    
    -- Store ped reference
    MiningPeds[pedType] = ped
    
    -- Setup targeting
    if config.targetOptions then
        exports.ox_target:addLocalEntity(ped, config.targetOptions)
    end
    
    -- Set model as no longer needed
    SetModelAsNoLongerNeeded(modelHash)
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Spawned ' .. pedType .. ' ped at ' .. tostring(config.coords))
    end
    
    return true
end

-- Spawn all mining peds
function SpawnAllMiningPeds()
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Spawning mining peds...')
    end
    
    local spawnedCount = 0
    
    for pedType, config in pairs(PedConfigs) do
        if SpawnPed(pedType, config) then
            spawnedCount = spawnedCount + 1
        end
    end
    
    local totalPeds = 0
    for _ in pairs(PedConfigs) do
        totalPeds = totalPeds + 1
    end
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Spawned ' .. spawnedCount .. '/' .. totalPeds .. ' peds')
    end
    
    return spawnedCount == totalPeds
end

-- Delete all mining peds
function DeleteAllMiningPeds()
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Deleting mining peds...')
    end
    
    for pedType, ped in pairs(MiningPeds) do
        if DoesEntityExist(ped) then
            -- Remove targeting
            exports.ox_target:removeLocalEntity(ped)
            
            -- Delete ped
            DeletePed(ped)
            MiningPeds[pedType] = nil
            
            if Config.Debug then
                print('^2[QBX Mining Client]^7 Deleted ' .. pedType .. ' ped')
            end
        end
    end
end

-- Check if ped exists and is valid
function IsPedValid(pedType)
    local ped = MiningPeds[pedType]
    return ped and DoesEntityExist(ped)
end

-- Get ped entity
function GetMiningPed(pedType)
    return MiningPeds[pedType]
end

-- Respawn a specific ped if it's missing
function RespawnPedIfNeeded(pedType)
    if not IsPedValid(pedType) then
        local config = PedConfigs[pedType]
        if config then
            SpawnPed(pedType, config)
        end
    end
end

-- Monitor peds and respawn if needed
function StartPedMonitoring()
    CreateThread(function()
        while true do
            Wait(30000) -- Check every 30 seconds
            
            for pedType, _ in pairs(PedConfigs) do
                RespawnPedIfNeeded(pedType)
            end
        end
    end)
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Started ped monitoring')
    end
end

-- Enhanced ped interactions
function SetupEnhancedPedInteractions()
    -- Add additional interaction options for each ped type
    
    -- Vendor ped - additional options
    if IsPedValid('vendor') then
        local vendorPed = GetMiningPed('vendor')
        
        exports.ox_target:addLocalEntity(vendorPed, {
            {
                name = 'mining_vendor_info',
                icon = 'fas fa-info-circle',
                label = locale('vendor_info'),
                onSelect = function()
                    ShowVendorInfo()
                end
            }
        })
    end
    
    -- Smelter ped - check queue option
    if IsPedValid('smelter') then
        local smelterPed = GetMiningPed('smelter')
        
        exports.ox_target:addLocalEntity(smelterPed, {
            {
                name = 'mining_smelter_queue',
                icon = 'fas fa-clock',
                label = locale('check_smelt_queue'),
                onSelect = function()
                    CheckSmeltQueue()
                end
            }
        })
    end
    
    -- Seller ped - price check option
    if IsPedValid('seller') then
        local sellerPed = GetMiningPed('seller')
        
        exports.ox_target:addLocalEntity(sellerPed, {
            {
                name = 'mining_seller_prices',
                icon = 'fas fa-chart-line',
                label = locale('check_prices'),
                onSelect = function()
                    ShowCurrentPrices()
                end
            }
        })
    end
end

-- Show vendor information
function ShowVendorInfo()
    local infoText = string.format('%s\n\n%s\n%s\n%s',
        locale('vendor_welcome'),
        locale('vendor_sells_tools'),
        locale('vendor_level_requirements'),
        locale('vendor_hours')
    )
    
    Bridge.AlertDialog({
        header = locale('vendor_info'),
        content = infoText,
        centered = true,
        cancel = true
    })
end

-- Check smelt queue
function CheckSmeltQueue()
    -- This would typically request queue info from server
    TriggerServerEvent('qbx_mining:client:requestSmeltQueue')
end

-- Show current prices
function ShowCurrentPrices()
    local priceMenu = {
        id = 'mining_current_prices',
        title = locale('current_prices'),
        options = {}
    }
    
    for itemName, price in pairs(Config.Selling.Prices) do
        local label = GetItemLabel(itemName)
        table.insert(priceMenu.options, {
            title = label,
            description = locale('price_per_unit', price),
            icon = GetItemIcon(itemName),
            disabled = true
        })
    end
    
    table.insert(priceMenu.options, {
        title = locale('close'),
        icon = 'fas fa-times',
        onSelect = function()
            lib.hideContext()
        end
    })
    
    Bridge.RegisterContext(priceMenu)
    Bridge.ShowContext({id = 'mining_current_prices'})
end

-- Ped animation management
function SetPedAnimation(pedType, animDict, animName, duration)
    local ped = GetMiningPed(pedType)
    if not IsPedValid(pedType) then return false end
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, duration or -1, 1, 0, false, false, false)
    return true
end

-- Make ped look at player
function MakePedLookAtPlayer(pedType)
    local ped = GetMiningPed(pedType)
    if not IsPedValid(pedType) then return false end
    
    local playerPed = PlayerPedId()
    TaskLookAtEntity(ped, playerPed, 3000, 0, 2)
    return true
end

-- Ped speech system
function MakePedSpeak(pedType, speechName, speechParam)
    local ped = GetMiningPed(pedType)
    if not IsPedValid(pedType) then return false end
    
    PlayAmbientSpeech1(ped, speechName, speechParam or 'SPEECH_PARAMS_STANDARD')
    return true
end

-- Setup ped speech for interactions
function SetupPedSpeech()
    -- Vendor greetings
    CreateThread(function()
        while true do
            Wait(math.random(30000, 60000)) -- Random interval
            
            if IsPedValid('vendor') then
                local playerPed = PlayerPedId()
                local vendorPed = GetMiningPed('vendor')
                local distance = Types.Utils.GetDistance(
                    GetEntityCoords(playerPed),
                    GetEntityCoords(vendorPed)
                )
                
                if distance <= 5.0 then
                    MakePedSpeak('vendor', 'GENERIC_HI', 'SPEECH_PARAMS_STANDARD')
                end
            end
        end
    end)
end

-- Initialize ped system
function InitializePedSystem()
    -- Spawn all peds
    local success = SpawnAllMiningPeds()
    
    if success then
        -- Start monitoring
        StartPedMonitoring()
        
        -- Setup enhanced interactions
        Wait(1000) -- Wait for peds to fully spawn
        SetupEnhancedPedInteractions()
        
        -- Setup speech system
        SetupPedSpeech()
        
        if Config.Debug then
            print('^2[QBX Mining Client]^7 Ped system initialized successfully')
        end
    else
        if Config.Debug then
            print('^1[QBX Mining ERROR]^7 Failed to initialize ped system')
        end
    end
    
    return success
end

-- Cleanup ped system
function CleanupPedSystem()
    DeleteAllMiningPeds()
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Ped system cleaned up')
    end
end

-- Event handlers
RegisterNetEvent('qbx_mining:client:smeltQueueResponse', function(queueData)
    if #queueData == 0 then
        Bridge.Notify(locale('no_active_smelts'), Types.NotifyType.INFO)
        return
    end
    
    local queueMenu = {
        id = 'mining_smelt_queue',
        title = locale('smelt_queue'),
        options = {}
    }
    
    for _, job in ipairs(queueData) do
        local timeText = job.timeRemaining > 0 and 
            Types.Utils.FormatTime(job.timeRemaining) or 
            locale('completed')
        
        table.insert(queueMenu.options, {
            title = string.format('%dx %s', job.amount, Config.Ores[job.oreType].label),
            description = locale('time_remaining', timeText),
            icon = GetOreIcon(job.oreType),
            disabled = true
        })
    end
    
    table.insert(queueMenu.options, {
        title = locale('close'),
        icon = 'fas fa-times',
        onSelect = function()
            lib.hideContext()
        end
    })
    
    Bridge.RegisterContext(queueMenu)
    Bridge.ShowContext({id = 'mining_smelt_queue'})
end)

-- Exports
exports('SpawnAllMiningPeds', SpawnAllMiningPeds)
exports('DeleteAllMiningPeds', DeleteAllMiningPeds)
exports('IsPedValid', IsPedValid)
exports('GetMiningPed', GetMiningPed)
exports('InitializePedSystem', InitializePedSystem)
exports('CleanupPedSystem', CleanupPedSystem)

-- Auto-initialize when this file loads
CreateThread(function()
    Wait(2000) -- Wait for other systems to load
    InitializePedSystem()
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        CleanupPedSystem()
    end
end)
