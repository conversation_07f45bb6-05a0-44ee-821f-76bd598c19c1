-- QBX Mining System - Item Registration and Handlers
local locale = lib.locale

-- Item use handlers for ox_inventory
local function RegisterItemHandlers()
    -- Pickaxe use handler
    exports.ox_inventory:registerHook('useItem', function(playerId, itemName, slotId, metadata)
        if string.find(itemName, 'pickaxe_') then
            -- Set as active pickaxe
            local player = MiningData.players[playerId]
            if player then
                player.currentPickaxe = itemName
                Bridge.SetMetadata(playerId, Config.Progression.MetadataKeys.pickaxe, itemName)
                Bridge.Notify(playerId, locale('pickaxe_equipped', itemName), Types.NotifyType.SUCCESS)
            end
            return false -- Don't consume the item
        end
        
        if string.find(itemName, 'handle_') then
            -- Set as active handle
            local player = MiningData.players[playerId]
            if player then
                player.currentHandle = itemName
                Bridge.SetMetadata(playerId, Config.Progression.MetadataKeys.handle, itemName)
                Bridge.Notify(playerId, locale('handle_equipped', itemName), Types.NotifyType.SUCCESS)
            end
            return false -- Don't consume the item
        end
        
        return true -- Allow other items to be consumed normally
    end, {
        print = false,
        itemFilter = {
            'pickaxe_basic', 'pickaxe_iron', 'pickaxe_steel', 'pickaxe_titanium', 'pickaxe_diamond',
            'handle_wood', 'handle_iron', 'handle_titanium'
        }
    })
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Item handlers registered successfully')
    end
end

-- Validate item metadata on server start
local function ValidateItems()
    local requiredItems = {
        -- Ores
        'coal_ore', 'copper_ore', 'iron_ore', 'silver_ore', 'gold_ore', 'diamond_ore',
        -- Ingots
        'coal', 'copper_ingot', 'iron_ingot', 'silver_ingot', 'gold_ingot', 'diamond_gem',
        -- Tools
        'pickaxe_basic', 'pickaxe_iron', 'pickaxe_steel', 'pickaxe_titanium', 'pickaxe_diamond',
        'handle_wood', 'handle_iron', 'handle_titanium'
    }
    
    local missingItems = {}
    
    for _, itemName in ipairs(requiredItems) do
        local item = exports.ox_inventory:Items(itemName)
        if not item then
            table.insert(missingItems, itemName)
        end
    end
    
    if #missingItems > 0 then
        print('^1[QBX Mining ERROR]^7 Missing required items in ox_inventory:')
        for _, itemName in ipairs(missingItems) do
            print('^1  - ' .. itemName .. '^7')
        end
        print('^1Please add these items to ox_inventory/data/items.lua^7')
        return false
    end
    
    if Config.Debug then
        print('^2[QBX Mining]^7 All required items validated successfully')
    end
    
    return true
end

-- Create item images directory and placeholder files
local function CreateItemImages()
    local itemImages = {
        -- Ores
        'coal_ore.png', 'copper_ore.png', 'iron_ore.png', 'silver_ore.png', 'gold_ore.png', 'diamond_ore.png',
        -- Ingots
        'coal.png', 'copper_ingot.png', 'iron_ingot.png', 'silver_ingot.png', 'gold_ingot.png', 'diamond_gem.png',
        -- Tools
        'pickaxe_basic.png', 'pickaxe_iron.png', 'pickaxe_steel.png', 'pickaxe_titanium.png', 'pickaxe_diamond.png',
        'handle_wood.png', 'handle_iron.png', 'handle_titanium.png'
    }
    
    -- Note: In a real implementation, you would copy actual PNG files to ox_inventory/web/images/
    -- For now, we'll just log what images are needed
    if Config.Debug then
        print('^2[QBX Mining]^7 Required item images for ox_inventory/web/images/:')
        for _, imageName in ipairs(itemImages) do
            print('^3  - ' .. imageName .. '^7')
        end
    end
end

-- Item durability management
local function HandleItemDurability(playerId, itemName, slotId, currentDurability, damage)
    local newDurability = math.max(0, currentDurability - damage)
    
    if newDurability <= 0 then
        -- Item breaks
        exports.ox_inventory:RemoveItem(playerId, itemName, 1, nil, slotId)
        
        -- Get item label for notification
        local itemLabel = itemName
        for _, pickaxe in ipairs(Config.Tools.Pickaxes) do
            if pickaxe.item == itemName then
                itemLabel = pickaxe.label
                break
            end
        end
        
        Bridge.Notify(playerId, locale('tool_broken', itemLabel), Types.NotifyType.WARNING)
        TriggerClientEvent(Types.Events.SERVER_TOOL_BROKEN, playerId)
        
        return false -- Item broken
    else
        -- Update durability
        exports.ox_inventory:SetDurability(playerId, slotId, newDurability)
        
        -- Warn player if durability is low
        if newDurability <= 10 then
            Bridge.Notify(playerId, locale('tool_low_durability'), Types.NotifyType.WARNING)
        end
        
        return true -- Item still usable
    end
end

-- Repair item function (for future expansion)
local function RepairItem(playerId, itemName, slotId, repairAmount)
    local item = exports.ox_inventory:GetSlot(playerId, slotId)
    if not item or item.name ~= itemName then
        return false
    end
    
    local currentDurability = item.metadata and item.metadata.durability or 0
    local maxDurability = 100 -- Default max durability
    
    -- Get max durability from config
    for _, pickaxe in ipairs(Config.Tools.Pickaxes) do
        if pickaxe.item == itemName then
            maxDurability = pickaxe.durability
            break
        end
    end
    
    local newDurability = math.min(maxDurability, currentDurability + repairAmount)
    exports.ox_inventory:SetDurability(playerId, slotId, newDurability)
    
    Bridge.Notify(playerId, locale('item_repaired'), Types.NotifyType.SUCCESS)
    return true
end

-- Get item tier for progression calculations
local function GetItemTier(itemName)
    for _, pickaxe in ipairs(Config.Tools.Pickaxes) do
        if pickaxe.item == itemName then
            return pickaxe.tier
        end
    end
    return 1
end

-- Check if player can use item based on level
local function CanUseItem(playerId, itemName)
    local player = MiningData.players[playerId]
    if not player then return false end
    
    local requiredLevel = 1
    
    -- Check pickaxe level requirement
    for _, pickaxe in ipairs(Config.Tools.Pickaxes) do
        if pickaxe.item == itemName then
            requiredLevel = pickaxe.minLevel
            break
        end
    end
    
    -- Check handle level requirement
    for _, handle in ipairs(Config.Tools.Handles) do
        if handle.item == itemName then
            requiredLevel = handle.minLevel
            break
        end
    end
    
    return player.level >= requiredLevel
end

-- Item combination system (for future expansion)
local function CombineItems(playerId, item1, item2)
    -- Example: Combine pickaxe + handle for enhanced stats
    -- This could be expanded for crafting system
    
    local isPickaxe1 = string.find(item1, 'pickaxe_')
    local isHandle1 = string.find(item1, 'handle_')
    local isPickaxe2 = string.find(item2, 'pickaxe_')
    local isHandle2 = string.find(item2, 'handle_')
    
    if (isPickaxe1 and isHandle2) or (isHandle1 and isPickaxe2) then
        -- Valid combination
        Bridge.Notify(playerId, locale('items_combined'), Types.NotifyType.SUCCESS)
        return true
    end
    
    return false
end

-- Export item utility functions
function GetItemData(itemName)
    -- Get item data from config
    for _, pickaxe in ipairs(Config.Tools.Pickaxes) do
        if pickaxe.item == itemName then
            return pickaxe
        end
    end
    
    for _, handle in ipairs(Config.Tools.Handles) do
        if handle.item == itemName then
            return handle
        end
    end
    
    return nil
end

-- Initialize items system
local function InitializeItems()
    if not ValidateItems() then
        print('^1[QBX Mining ERROR]^7 Item validation failed! Resource may not work properly.')
        return false
    end
    
    RegisterItemHandlers()
    CreateItemImages()
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Items system initialized successfully')
    end
    
    return true
end

-- Event handlers
RegisterNetEvent('qbx_mining:server:repairItem', function(itemName, slotId, repairAmount)
    local src = source
    RepairItem(src, itemName, slotId, repairAmount or 25)
end)

RegisterNetEvent('qbx_mining:server:combineItems', function(item1, item2)
    local src = source
    CombineItems(src, item1, item2)
end)

-- Exports
exports('InitializeItems', InitializeItems)
exports('HandleItemDurability', HandleItemDurability)
exports('RepairItem', RepairItem)
exports('GetItemTier', GetItemTier)
exports('CanUseItem', CanUseItem)
exports('GetItemData', GetItemData)

-- Auto-initialize when this file is loaded
CreateThread(function()
    Wait(1000) -- Wait for ox_inventory to be ready
    InitializeItems()
end)
