return {
    -- General
    ['mining_job'] = 'Mining Job',
    ['level'] = 'Level',
    ['xp'] = 'XP',
    ['next_level'] = 'Next Level',
    
    -- Mining Actions
    ['mine_node'] = 'Mine %s',
    ['mining_in_progress'] = 'Mining...',
    ['node_depleted'] = 'Node depleted, respawning in %s seconds',
    ['level_required'] = 'Level %s required to mine %s',
    ['tool_required'] = 'You need a pickaxe to mine',
    ['tool_broken'] = 'Your %s has broken!',
    ['mining_success'] = 'You mined %sx %s',
    ['mining_failed'] = 'Mining failed',
    ['rare_find'] = 'Rare find! You got extra ore!',
    
    -- Progression
    ['level_up'] = 'Level Up! You are now level %s',
    ['xp_gained'] = '+%s XP',
    ['max_level_reached'] = 'Maximum level reached!',
    
    -- Vendor
    ['open_vendor'] = 'Open Mining Vendor',
    ['vendor_title'] = 'Mining Equipment Vendor',
    ['pickaxes'] = 'Pickaxes',
    ['handles'] = 'Handles',
    ['supplies'] = 'Supplies',
    ['purchase'] = 'Purchase',
    ['price'] = 'Price: $%s',
    ['level_req'] = 'Level %s Required',
    ['insufficient_funds'] = 'Insufficient funds',
    ['insufficient_level'] = 'Insufficient level',
    ['purchase_success'] = 'Purchased %s for $%s',
    ['inventory_full'] = 'Inventory full',
    
    -- Smelting
    ['open_smelter'] = 'Smelt Ores',
    ['smelter_title'] = 'Ore Smelting',
    ['select_ore'] = 'Select Ore Type',
    ['amount'] = 'Amount',
    ['coal_required'] = 'Coal Required: %s',
    ['time_required'] = 'Time Required: %s minutes',
    ['start_smelting'] = 'Start Smelting',
    ['smelting_in_progress'] = 'Smelting in progress...',
    ['smelting_complete'] = 'Smelting complete! Received %sx %s',
    ['smelting_cancelled'] = 'Smelting cancelled',
    ['insufficient_ore'] = 'Insufficient ore',
    ['insufficient_coal'] = 'Insufficient coal',
    ['max_batch_exceeded'] = 'Maximum batch size is %s',
    ['smelting_queue_full'] = 'Smelting queue is full',
    
    -- Selling
    ['open_seller'] = 'Sell Ingots',
    ['seller_title'] = 'Ingot Buyer',
    ['sell_all'] = 'Sell All',
    ['sell_selected'] = 'Sell Selected',
    ['total_value'] = 'Total Value: $%s',
    ['tax'] = 'Tax: $%s',
    ['final_payout'] = 'Final Payout: $%s',
    ['sell_success'] = 'Sold items for $%s',
    ['nothing_to_sell'] = 'Nothing to sell',
    ['no_items_selected'] = 'No items selected',
    
    -- Errors
    ['too_far'] = 'You are too far from the mining node',
    ['no_line_of_sight'] = 'No clear line of sight to the node',
    ['rate_limited'] = 'You are mining too fast, slow down',
    ['invalid_node'] = 'Invalid mining node',
    ['node_not_ready'] = 'This node is not ready for mining',
    ['server_error'] = 'Server error occurred',
    
    -- Admin Commands
    ['admin_level_set'] = 'Set %s mining level to %s',
    ['admin_xp_added'] = 'Added %s XP to %s',
    ['admin_nodes_regenerated'] = 'All mining nodes regenerated',
    ['admin_debug_enabled'] = 'Mining debug mode enabled',
    ['admin_debug_disabled'] = 'Mining debug mode disabled',
    ['admin_invalid_player'] = 'Invalid player ID',
    ['admin_invalid_level'] = 'Invalid level (1-%s)',
    ['admin_invalid_xp'] = 'Invalid XP amount',
    
    -- UI Elements
    ['close'] = 'Close',
    ['confirm'] = 'Confirm',
    ['cancel'] = 'Cancel',
    ['back'] = 'Back',
    ['next'] = 'Next',
    ['previous'] = 'Previous',
    ['loading'] = 'Loading...',
    ['please_wait'] = 'Please wait...',
    
    -- Tool Descriptions
    ['pickaxe_basic_desc'] = 'A basic mining pickaxe for beginners',
    ['pickaxe_iron_desc'] = 'An improved iron pickaxe with better efficiency',
    ['pickaxe_steel_desc'] = 'A durable steel pickaxe for serious miners',
    ['pickaxe_titanium_desc'] = 'A high-tech titanium pickaxe for advanced mining',
    ['pickaxe_diamond_desc'] = 'The ultimate mining tool with diamond-tipped edges',
    ['handle_wood_desc'] = 'A basic wooden handle for mining tools',
    ['handle_iron_desc'] = 'An improved iron handle that provides better grip and efficiency',
    ['handle_titanium_desc'] = 'A lightweight titanium handle with superior performance',
    
    -- Ore Descriptions
    ['coal_ore_desc'] = 'Raw coal ore that can be smelted into coal',
    ['copper_ore_desc'] = 'Raw copper ore that can be smelted into copper ingots',
    ['iron_ore_desc'] = 'Raw iron ore that can be smelted into iron ingots',
    ['silver_ore_desc'] = 'Raw silver ore that can be smelted into silver ingots',
    ['gold_ore_desc'] = 'Raw gold ore that can be smelted into gold ingots',
    ['diamond_ore_desc'] = 'Raw diamond ore that can be cut into precious gems',
    
    -- Ingot Descriptions
    ['coal_desc'] = 'Refined coal used as fuel for smelting',
    ['copper_ingot_desc'] = 'Refined copper ingot ready for sale',
    ['iron_ingot_desc'] = 'Refined iron ingot ready for sale',
    ['silver_ingot_desc'] = 'Refined silver ingot ready for sale',
    ['gold_ingot_desc'] = 'Refined gold ingot ready for sale',
    ['diamond_gem_desc'] = 'Perfectly cut diamond gem ready for sale',
}
