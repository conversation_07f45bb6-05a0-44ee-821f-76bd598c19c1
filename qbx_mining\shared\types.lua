-- Shared Types and Constants for QBX Mining System

Types = {}

-- Mining Node States
Types.NodeState = {
    ACTIVE = 'active',
    DEPLETED = 'depleted',
    RESPAWNING = 'respawning'
}

-- Ore Types
Types.OreType = {
    COAL = 'coal',
    COPPER = 'copper',
    IRON = 'iron',
    SILVER = 'silver',
    GOLD = 'gold',
    DIAMOND = 'diamond'
}

-- Tool Types
Types.ToolType = {
    PICKAXE = 'pickaxe',
    HANDLE = 'handle'
}

-- Pickaxe Tiers
Types.PickaxeTier = {
    BASIC = 1,
    IRON = 2,
    STEEL = 3,
    TITANIUM = 4,
    DIAMOND = 5
}

-- Smelting States
Types.SmeltState = {
    QUEUED = 'queued',
    PROCESSING = 'processing',
    COMPLETED = 'completed',
    CANCELLED = 'cancelled'
}

-- Transaction Types
Types.TransactionType = {
    PURCHASE = 'purchase',
    SALE = 'sale',
    SMELTING = 'smelting'
}

-- Event Names
Types.Events = {
    -- Client to Server
    CLIENT_MINE_NODE = 'qbx_mining:client:mineNode',
    CLIENT_PURCHASE_ITEM = 'qbx_mining:client:purchaseItem',
    CLIENT_START_SMELT = 'qbx_mining:client:startSmelt',
    CLIENT_SELL_ITEMS = 'qbx_mining:client:sellItems',
    CLIENT_REQUEST_DATA = 'qbx_mining:client:requestData',
    
    -- Server to Client
    SERVER_NODE_UPDATE = 'qbx_mining:server:nodeUpdate',
    SERVER_LEVEL_UPDATE = 'qbx_mining:server:levelUpdate',
    SERVER_SMELT_UPDATE = 'qbx_mining:server:smeltUpdate',
    SERVER_SYNC_NODES = 'qbx_mining:server:syncNodes',
    SERVER_TOOL_BROKEN = 'qbx_mining:server:toolBroken',
    
    -- Internal Events
    INTERNAL_NODE_RESPAWN = 'qbx_mining:internal:nodeRespawn',
    INTERNAL_SMELT_COMPLETE = 'qbx_mining:internal:smeltComplete',
    INTERNAL_SAVE_DATA = 'qbx_mining:internal:saveData'
}

-- Notification Types
Types.NotifyType = {
    SUCCESS = 'success',
    ERROR = 'error',
    WARNING = 'warning',
    INFO = 'inform'
}

-- Mining Result Types
Types.MiningResult = {
    SUCCESS = 'success',
    FAILED = 'failed',
    TOOL_BROKEN = 'tool_broken',
    LEVEL_REQUIRED = 'level_required',
    RATE_LIMITED = 'rate_limited',
    TOO_FAR = 'too_far',
    NO_LOS = 'no_los',
    INVALID_NODE = 'invalid_node'
}

-- Utility Functions
Types.Utils = {}

-- Generate unique node ID
function Types.Utils.GenerateNodeId()
    return 'node_' .. math.random(100000, 999999) .. '_' .. GetGameTimer()
end

-- Generate unique smelt job ID
function Types.Utils.GenerateSmeltId()
    return 'smelt_' .. math.random(100000, 999999) .. '_' .. GetGameTimer()
end

-- Calculate distance between two points
function Types.Utils.GetDistance(pos1, pos2)
    return #(pos1 - pos2)
end

-- Check if position is within bounds
function Types.Utils.IsWithinBounds(pos, center, radius)
    return Types.Utils.GetDistance(pos, center) <= radius
end

-- Format time in seconds to readable string
function Types.Utils.FormatTime(seconds)
    if seconds < 60 then
        return string.format('%ds', seconds)
    elseif seconds < 3600 then
        return string.format('%dm %ds', math.floor(seconds / 60), seconds % 60)
    else
        return string.format('%dh %dm', math.floor(seconds / 3600), math.floor((seconds % 3600) / 60))
    end
end

-- Format number with commas
function Types.Utils.FormatNumber(num)
    local formatted = tostring(num)
    local k
    while true do
        formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", '%1,%2')
        if k == 0 then break end
    end
    return formatted
end

-- Round number to specified decimal places
function Types.Utils.Round(num, decimals)
    local mult = 10 ^ (decimals or 0)
    return math.floor(num * mult + 0.5) / mult
end

-- Check if table contains value
function Types.Utils.TableContains(table, value)
    for _, v in pairs(table) do
        if v == value then return true end
    end
    return false
end

-- Deep copy table
function Types.Utils.DeepCopy(orig)
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[Types.Utils.DeepCopy(orig_key)] = Types.Utils.DeepCopy(orig_value)
        end
        setmetatable(copy, Types.Utils.DeepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Validate ore type
function Types.Utils.IsValidOreType(oreType)
    for _, validType in pairs(Types.OreType) do
        if oreType == validType then return true end
    end
    return false
end

-- Validate tool type
function Types.Utils.IsValidToolType(toolType)
    for _, validType in pairs(Types.ToolType) do
        if toolType == validType then return true end
    end
    return false
end

-- Get ore tier (for progression)
function Types.Utils.GetOreTier(oreType)
    local tiers = {
        [Types.OreType.COAL] = 1,
        [Types.OreType.COPPER] = 2,
        [Types.OreType.IRON] = 3,
        [Types.OreType.SILVER] = 4,
        [Types.OreType.GOLD] = 5,
        [Types.OreType.DIAMOND] = 6
    }
    return tiers[oreType] or 1
end

-- Constants
Types.Constants = {
    MAX_MINING_DISTANCE = 4.0,
    MAX_INTERACTION_DISTANCE = 2.0,
    DEFAULT_NODE_HEALTH = 100,
    MIN_LEVEL = 1,
    MAX_LEVEL = 50,
    DEFAULT_XP = 0,
    SAVE_INTERVAL = 300000, -- 5 minutes in ms
    NODE_SYNC_INTERVAL = 30000, -- 30 seconds in ms
    MAX_SMELT_QUEUE_SIZE = 10,
    DEFAULT_ANIMATION_DURATION = 1600,
    PARTICLE_DURATION = 2000,
    SOUND_RANGE = 10.0
}

return Types
