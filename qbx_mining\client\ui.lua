-- QBX Mining System - UI Client
local locale = lib.locale

-- UI State Management
local UIState = {
    vendorOpen = false,
    smelterOpen = false,
    sellerOpen = false,
    currentMenu = nil
}

-- Vendor Menu System
function OpenVendorMenu()
    if UIState.vendorOpen then return end
    
    UIState.vendorOpen = true
    UIState.currentMenu = 'vendor'
    
    local vendorMenu = {
        id = 'mining_vendor',
        title = locale('vendor_title'),
        options = {}
    }
    
    -- Add category options
    for _, category in ipairs(Config.Vendor.Categories) do
        table.insert(vendorMenu.options, {
            title = locale(category:lower()),
            description = locale('browse_category', category),
            icon = GetCategoryIcon(category),
            onSelect = function()
                OpenCategoryMenu(category)
            end
        })
    end
    
    -- Add close option
    table.insert(vendorMenu.options, {
        title = locale('close'),
        icon = 'fas fa-times',
        onSelect = function()
            CloseVendorMenu()
        end
    })
    
    Bridge.RegisterContext(vendorMenu)
    Bridge.ShowContext({id = 'mining_vendor'})
end

-- Category menu (Pickaxes, Handles, Supplies)
function OpenCategoryMenu(category)
    local categoryMenu = {
        id = 'mining_category_' .. category:lower(),
        title = locale('vendor_title') .. ' - ' .. (locale(category:lower()) or category),
        menu = 'mining_vendor',
        options = {}
    }
    
    local items = GetCategoryItems(category)
    
    for _, item in ipairs(items) do
        local canPurchase = PlayerLevel.level >= (item.minLevel or 1)
        local levelText = item.minLevel and item.minLevel > 1 and locale('level_req', item.minLevel) or ''
        
        table.insert(categoryMenu.options, {
            title = item.label,
            description = string.format('%s\n%s\n%s', 
                item.description or '', 
                locale('price', item.price),
                levelText
            ),
            icon = GetItemIcon(item.item),
            disabled = not canPurchase,
            onSelect = function()
                if canPurchase then
                    ConfirmPurchase(item, category)
                else
                    Bridge.Notify(locale('insufficient_level'), Types.NotifyType.ERROR)
                end
            end
        })
    end
    
    Bridge.RegisterContext(categoryMenu)
    Bridge.ShowContext({id = 'mining_category_' .. category:lower()})
end

-- Purchase confirmation
function ConfirmPurchase(item, category)
    local confirmMenu = {
        id = 'mining_purchase_confirm',
        title = locale('confirm_purchase'),
        menu = 'mining_category_' .. category:lower(),
        options = {
            {
                title = locale('confirm'),
                description = locale('purchase_item_for', item.label, item.price),
                icon = 'fas fa-check',
                onSelect = function()
                    TriggerServerEvent(Types.Events.CLIENT_PURCHASE_ITEM, item, category)
                    CloseVendorMenu()
                end
            },
            {
                title = locale('cancel'),
                icon = 'fas fa-times',
                onSelect = function()
                    lib.showContext('mining_category_' .. category:lower())
                end
            }
        }
    }
    
    Bridge.RegisterContext(confirmMenu)
    Bridge.ShowContext({id = 'mining_purchase_confirm'})
end

-- Get items for category
function GetCategoryItems(category)
    if category == 'Pickaxes' then
        return Config.Tools.Pickaxes
    elseif category == 'Handles' then
        return Config.Tools.Handles
    elseif category == 'Supplies' then
        return Config.Vendor.Supplies
    end
    return {}
end

-- Close vendor menu
function CloseVendorMenu()
    UIState.vendorOpen = false
    UIState.currentMenu = nil
    lib.hideContext()
end

-- Smelting Menu System
function OpenSmelterMenu()
    if UIState.smelterOpen then return end
    
    UIState.smelterOpen = true
    UIState.currentMenu = 'smelter'
    
    -- Get player's ore inventory
    local playerOres = GetPlayerOres()
    
    if #playerOres == 0 then
        Bridge.Notify(locale('no_ores_to_smelt'), Types.NotifyType.ERROR)
        return
    end
    
    local smelterMenu = {
        id = 'mining_smelter',
        title = locale('smelter_title'),
        options = {}
    }
    
    -- Add ore options
    for _, ore in ipairs(playerOres) do
        local oreConfig = Config.Ores[ore.type]
        if oreConfig then
            table.insert(smelterMenu.options, {
                title = oreConfig.label,
                description = locale('available_amount', ore.count),
                icon = GetOreIcon(ore.type),
                onSelect = function()
                    OpenSmeltAmountMenu(ore.type, ore.count)
                end
            })
        end
    end
    
    -- Add close option
    table.insert(smelterMenu.options, {
        title = locale('close'),
        icon = 'fas fa-times',
        onSelect = function()
            CloseSmelterMenu()
        end
    })
    
    Bridge.RegisterContext(smelterMenu)
    Bridge.ShowContext({id = 'mining_smelter'})
end

-- Smelt amount selection
function OpenSmeltAmountMenu(oreType, maxAmount)
    local input = Bridge.InputDialog({
        heading = locale('select_smelt_amount'),
        rows = {
            {
                type = 'number',
                label = locale('amount'),
                placeholder = '1',
                min = 1,
                max = math.min(maxAmount, Config.Smelting.MaxBatch),
                required = true
            }
        }
    })
    
    if input and input[1] then
        local amount = tonumber(input[1])
        if amount and amount > 0 then
            ConfirmSmelt(oreType, amount)
        end
    else
        lib.showContext('mining_smelter')
    end
end

-- Confirm smelting
function ConfirmSmelt(oreType, amount)
    local coalRequired = Config.Smelting.RequiresCoal and Config.Smelting.CoalPerBatch(amount) or 0
    local timeRequired = math.max(
        Config.Smelting.MinSeconds,
        math.min(Config.Smelting.MaxSeconds, amount * Config.Smelting.TimePerOreSeconds)
    )
    
    local confirmMenu = {
        id = 'mining_smelt_confirm',
        title = locale('confirm_smelting'),
        menu = 'mining_smelter',
        options = {
            {
                title = locale('confirm'),
                description = string.format('%s\n%s\n%s',
                    locale('smelting_amount', amount, Config.Ores[oreType].label),
                    coalRequired > 0 and locale('coal_required', coalRequired) or locale('no_coal_required'),
                    locale('time_required', Types.Utils.FormatTime(timeRequired))
                ),
                icon = 'fas fa-fire',
                onSelect = function()
                    TriggerServerEvent(Types.Events.CLIENT_START_SMELT, oreType, amount)
                    CloseSmelterMenu()
                end
            },
            {
                title = locale('cancel'),
                icon = 'fas fa-times',
                onSelect = function()
                    lib.showContext('mining_smelter')
                end
            }
        }
    }
    
    Bridge.RegisterContext(confirmMenu)
    Bridge.ShowContext({id = 'mining_smelt_confirm'})
end

-- Close smelter menu
function CloseSmelterMenu()
    UIState.smelterOpen = false
    UIState.currentMenu = nil
    lib.hideContext()
end

-- Selling Menu System
function OpenSellerMenu()
    if UIState.sellerOpen then return end
    
    UIState.sellerOpen = true
    UIState.currentMenu = 'seller'
    
    -- Get player's sellable items
    local sellableItems = GetPlayerSellableItems()
    
    if #sellableItems == 0 then
        Bridge.Notify(locale('nothing_to_sell'), Types.NotifyType.ERROR)
        return
    end
    
    local sellerMenu = {
        id = 'mining_seller',
        title = locale('seller_title'),
        options = {}
    }
    
    -- Add individual item options
    for _, item in ipairs(sellableItems) do
        local totalValue = item.price * item.count
        
        table.insert(sellerMenu.options, {
            title = item.label,
            description = string.format('%s\n%s\n%s',
                locale('available_amount', item.count),
                locale('price_each', item.price),
                locale('total_value', totalValue)
            ),
            icon = GetItemIcon(item.item),
            onSelect = function()
                SelectSellAmount(item)
            end
        })
    end
    
    -- Add sell all option
    local totalValue = CalculateTotalValue(sellableItems)
    if totalValue > 0 then
        table.insert(sellerMenu.options, {
            title = locale('sell_all'),
            description = locale('total_value', totalValue),
            icon = 'fas fa-coins',
            onSelect = function()
                ConfirmSellAll(sellableItems)
            end
        })
    end
    
    -- Add close option
    table.insert(sellerMenu.options, {
        title = locale('close'),
        icon = 'fas fa-times',
        onSelect = function()
            CloseSellerMenu()
        end
    })
    
    Bridge.RegisterContext(sellerMenu)
    Bridge.ShowContext({id = 'mining_seller'})
end

-- Select sell amount for individual item
function SelectSellAmount(item)
    local input = Bridge.InputDialog({
        heading = locale('select_sell_amount'),
        rows = {
            {
                type = 'number',
                label = locale('amount'),
                placeholder = tostring(item.count),
                min = 1,
                max = item.count,
                required = true
            }
        }
    })
    
    if input and input[1] then
        local amount = tonumber(input[1])
        if amount and amount > 0 and amount <= item.count then
            local sellItems = {{
                item = item.item,
                amount = amount
            }}
            TriggerServerEvent(Types.Events.CLIENT_SELL_ITEMS, sellItems)
            CloseSellerMenu()
        end
    else
        lib.showContext('mining_seller')
    end
end

-- Confirm sell all
function ConfirmSellAll(items)
    local sellItems = {}
    local totalValue = 0
    
    for _, item in ipairs(items) do
        table.insert(sellItems, {
            item = item.item,
            amount = item.count
        })
        totalValue = totalValue + (item.price * item.count)
    end
    
    local tax = totalValue * Config.Selling.TaxRate
    local finalPayout = totalValue - tax
    
    local confirmMenu = {
        id = 'mining_sell_all_confirm',
        title = locale('confirm_sell_all'),
        menu = 'mining_seller',
        options = {
            {
                title = locale('confirm'),
                description = string.format('%s\n%s\n%s',
                    locale('total_value', totalValue),
                    tax > 0 and locale('tax', tax) or '',
                    locale('final_payout', finalPayout)
                ),
                icon = 'fas fa-check',
                onSelect = function()
                    TriggerServerEvent(Types.Events.CLIENT_SELL_ITEMS, sellItems)
                    CloseSellerMenu()
                end
            },
            {
                title = locale('cancel'),
                icon = 'fas fa-times',
                onSelect = function()
                    lib.showContext('mining_seller')
                end
            }
        }
    }
    
    Bridge.RegisterContext(confirmMenu)
    Bridge.ShowContext({id = 'mining_sell_all_confirm'})
end

-- Close seller menu
function CloseSellerMenu()
    UIState.sellerOpen = false
    UIState.currentMenu = nil
    lib.hideContext()
end

-- Utility Functions
function GetPlayerOres()
    local ores = {}
    
    for oreType, oreConfig in pairs(Config.Ores) do
        local count = exports.ox_inventory:GetItemCount(oreConfig.item)
        if count > 0 then
            table.insert(ores, {
                type = oreType,
                item = oreConfig.item,
                label = oreConfig.label,
                count = count
            })
        end
    end
    
    return ores
end

function GetPlayerSellableItems()
    local items = {}
    
    for itemName, price in pairs(Config.Selling.Prices) do
        local count = exports.ox_inventory:GetItemCount(itemName)
        if count > 0 then
            local label = GetItemLabel(itemName)
            table.insert(items, {
                item = itemName,
                label = label,
                count = count,
                price = price
            })
        end
    end
    
    return items
end

function GetItemLabel(itemName)
    -- Get label from config
    for _, ingot in pairs(Config.Ingots) do
        if ingot.item == itemName then
            return ingot.label
        end
    end
    
    -- Fallback to item name
    return itemName:gsub('_', ' '):gsub('^%l', string.upper)
end

function CalculateTotalValue(items)
    local total = 0
    for _, item in ipairs(items) do
        total = total + (item.price * item.count)
    end
    return total
end

function GetCategoryIcon(category)
    local icons = {
        Pickaxes = 'fas fa-hammer',
        Handles = 'fas fa-grip-lines',
        Supplies = 'fas fa-box'
    }
    return icons[category] or 'fas fa-question'
end

function GetItemIcon(itemName)
    if string.find(itemName, 'pickaxe') then
        return 'fas fa-hammer'
    elseif string.find(itemName, 'handle') then
        return 'fas fa-grip-lines'
    elseif string.find(itemName, 'ore') then
        return 'fas fa-gem'
    elseif string.find(itemName, 'ingot') or string.find(itemName, 'gem') then
        return 'fas fa-coins'
    else
        return 'fas fa-cube'
    end
end

function GetOreIcon(oreType)
    local icons = {
        [Types.OreType.COAL] = 'fas fa-fire',
        [Types.OreType.COPPER] = 'fas fa-gem',
        [Types.OreType.IRON] = 'fas fa-gem',
        [Types.OreType.SILVER] = 'fas fa-gem',
        [Types.OreType.GOLD] = 'fas fa-gem',
        [Types.OreType.DIAMOND] = 'fas fa-diamond'
    }
    return icons[oreType] or 'fas fa-gem'
end

-- Progress Bar System
function ShowMiningProgress(duration, label)
    return Bridge.ProgressBar({
        duration = duration,
        label = label or locale('mining_in_progress'),
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = {
            dict = Config.Animations.Dict,
            clip = Config.Animations.Swing
        }
    })
end

function ShowSmeltingProgress(duration, oreType, amount)
    return Bridge.ProgressBar({
        duration = duration * 1000, -- Convert to milliseconds
        label = locale('smelting_progress', amount, Config.Ores[oreType].label),
        useWhileDead = false,
        canCancel = false, -- Smelting cannot be cancelled after 3 seconds
        disable = {
            car = true,
            move = false, -- Allow movement during smelting
            combat = true
        }
    })
end

-- Level Up Notification
function ShowLevelUpNotification(newLevel)
    Bridge.Notify(locale('level_up', newLevel), Types.NotifyType.SUCCESS, 8000)
    
    -- Play level up sound
    PlaySoundFrontend(-1, 'RANK_UP', 'HUD_AWARDS', true)
    
    -- Show level up effect (optional)
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Player reached level ' .. newLevel)
    end
end

-- XP Gain Notification
function ShowXPGainNotification(amount)
    Bridge.Notify(locale('xp_gained', amount), Types.NotifyType.SUCCESS, 3000)
end

-- Event Handlers
RegisterNetEvent(Types.Events.SERVER_SMELT_UPDATE, function(smeltData)
    if smeltData.state == Types.SmeltState.PROCESSING then
        -- Show smelting progress if player is nearby smelter
        local playerCoords = GetEntityCoords(PlayerPedId())
        local smelterCoords = Config.Smelting.Ped.coords
        local distance = Types.Utils.GetDistance(playerCoords, vec3(smelterCoords.x, smelterCoords.y, smelterCoords.z))
        
        if distance <= 10.0 then
            ShowSmeltingProgress(smeltData.timeRemaining, smeltData.oreType, smeltData.amount)
        end
    end
end)

RegisterNetEvent(Types.Events.SERVER_LEVEL_UPDATE, function(levelData)
    local oldLevel = PlayerLevel.level
    PlayerLevel = levelData
    
    -- Show level up notification if level increased
    if levelData.level > oldLevel then
        ShowLevelUpNotification(levelData.level)
    end
end)

-- Exports
exports('OpenVendorMenu', OpenVendorMenu)
exports('OpenSmelterMenu', OpenSmelterMenu)
exports('OpenSellerMenu', OpenSellerMenu)
exports('ShowMiningProgress', ShowMiningProgress)
exports('ShowLevelUpNotification', ShowLevelUpNotification)
exports('ShowXPGainNotification', ShowXPGainNotification)
