-- QBX Mining System - Main Server File
local locale = lib.locale

-- Global Variables
MiningData = {
    nodes = {},
    players = {},
    smeltQueue = {},
    lastSave = 0
}

-- Initialize the mining system
local function InitializeMining()
    if Config.Debug then
        print('^2[QBX Mining]^7 Initializing mining system...')
    end
    
    -- Load persistent data
    TriggerEvent('qbx_mining:server:loadData')
    
    -- Generate mining nodes
    TriggerEvent('qbx_mining:server:generateNodes')
    
    -- Start periodic saves
    CreateThread(function()
        while true do
            Wait(Types.Constants.SAVE_INTERVAL)
            TriggerEvent('qbx_mining:server:saveData')
        end
    end)
    
    -- Start node sync
    CreateThread(function()
        while true do
            Wait(Types.Constants.NODE_SYNC_INTERVAL)
            TriggerEvent('qbx_mining:server:syncNodes')
        end
    end)
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Mining system initialized successfully!')
    end
end

-- Player connection handlers
RegisterNetEvent('QBCore:Server:PlayerLoaded', function(Player)
    local src = Player.PlayerData.source
    local citizenid = Player.PlayerData.citizenid
    
    -- Initialize player mining data
    MiningData.players[src] = {
        citizenid = citizenid,
        level = Bridge.GetMetadata(src, Config.Progression.MetadataKeys.level) or 1,
        xp = Bridge.GetMetadata(src, Config.Progression.MetadataKeys.xp) or 0,
        lastHit = 0,
        currentPickaxe = Bridge.GetMetadata(src, Config.Progression.MetadataKeys.pickaxe) or nil,
        currentHandle = Bridge.GetMetadata(src, Config.Progression.MetadataKeys.handle) or nil
    }
    
    -- Sync nodes to player
    TriggerClientEvent(Types.Events.SERVER_SYNC_NODES, src, MiningData.nodes)
    
    if Config.Debug then
        print('^2[QBX Mining]^7 Player ' .. citizenid .. ' loaded with level ' .. MiningData.players[src].level)
    end
end)

RegisterNetEvent('QBCore:Server:OnPlayerUnload', function(src)
    if MiningData.players[src] then
        -- Save player data before removing
        local playerData = MiningData.players[src]
        Bridge.SetMetadata(src, Config.Progression.MetadataKeys.level, playerData.level)
        Bridge.SetMetadata(src, Config.Progression.MetadataKeys.xp, playerData.xp)
        
        MiningData.players[src] = nil
        
        if Config.Debug then
            print('^2[QBX Mining]^7 Player data saved and removed for source: ' .. src)
        end
    end
end)

-- Mining node hit event
RegisterNetEvent(Types.Events.CLIENT_MINE_NODE, function(nodeId, playerPos)
    local src = source
    local player = MiningData.players[src]
    
    if not player then
        Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
        return
    end
    
    -- Validate the mining attempt
    local result = exports.qbx_mining:ValidateMining(src, nodeId, playerPos)
    
    if result.success then
        -- Process the mining
        exports.qbx_mining:ProcessMining(src, nodeId, result.node)
    else
        -- Send error to client
        local errorMsg = locale(result.error) or locale('server_error')
        Bridge.Notify(src, errorMsg, Types.NotifyType.ERROR)
    end
end)

-- Purchase item event
RegisterNetEvent(Types.Events.CLIENT_PURCHASE_ITEM, function(itemData, category)
    local src = source
    local player = MiningData.players[src]
    
    if not player then
        Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
        return
    end
    
    -- Process the purchase
    exports.qbx_mining:ProcessPurchase(src, itemData, category)
end)

-- Start smelting event
RegisterNetEvent(Types.Events.CLIENT_START_SMELT, function(oreType, amount)
    local src = source
    local player = MiningData.players[src]
    
    if not player then
        Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
        return
    end
    
    -- Process the smelting request
    exports.qbx_mining:ProcessSmelting(src, oreType, amount)
end)

-- Sell items event
RegisterNetEvent(Types.Events.CLIENT_SELL_ITEMS, function(items)
    local src = source
    local player = MiningData.players[src]
    
    if not player then
        Bridge.Notify(src, locale('server_error'), Types.NotifyType.ERROR)
        return
    end
    
    -- Process the sale
    exports.qbx_mining:ProcessSale(src, items)
end)

-- Request player data event
RegisterNetEvent(Types.Events.CLIENT_REQUEST_DATA, function()
    local src = source
    local player = MiningData.players[src]
    
    if player then
        TriggerClientEvent(Types.Events.SERVER_LEVEL_UPDATE, src, {
            level = player.level,
            xp = player.xp,
            xpToNext = exports.qbx_mining:CalculateXPToNext(player.level)
        })
    end
end)

-- Internal events
RegisterNetEvent('qbx_mining:server:generateNodes', function()
    exports.qbx_mining:GenerateNodes()
end)

RegisterNetEvent('qbx_mining:server:syncNodes', function()
    -- Sync nodes to all players
    for src, _ in pairs(MiningData.players) do
        TriggerClientEvent(Types.Events.SERVER_SYNC_NODES, src, MiningData.nodes)
    end
end)

RegisterNetEvent('qbx_mining:server:saveData', function()
    exports.qbx_mining:SavePersistentData()
end)

RegisterNetEvent('qbx_mining:server:loadData', function()
    exports.qbx_mining:LoadPersistentData()
end)

-- Admin Commands
lib.addCommand('mining', {
    help = 'Mining system admin commands',
    params = {
        { name = 'action', type = 'string', help = 'setlevel/addxp/regen/debug' },
        { name = 'target', type = 'number', help = 'Player ID (for setlevel/addxp)', optional = true },
        { name = 'value', type = 'number', help = 'Level/XP amount', optional = true }
    },
    restricted = 'group.admin'
}, function(source, args)
    local src = source
    local action = args.action:lower()
    
    if action == 'setlevel' then
        if not args.target or not args.value then
            Bridge.Notify(src, 'Usage: /mining setlevel [playerid] [level]', Types.NotifyType.ERROR)
            return
        end
        
        local targetSrc = args.target
        local level = math.max(1, math.min(Config.Progression.MaxLevel, args.value))
        
        if MiningData.players[targetSrc] then
            MiningData.players[targetSrc].level = level
            Bridge.SetMetadata(targetSrc, Config.Progression.MetadataKeys.level, level)
            
            TriggerClientEvent(Types.Events.SERVER_LEVEL_UPDATE, targetSrc, {
                level = level,
                xp = MiningData.players[targetSrc].xp,
                xpToNext = exports.qbx_mining:CalculateXPToNext(level)
            })
            
            Bridge.Notify(src, locale('admin_level_set', Bridge.GetPlayerName(targetSrc), level), Types.NotifyType.SUCCESS)
            Bridge.Notify(targetSrc, locale('level_up', level), Types.NotifyType.SUCCESS)
        else
            Bridge.Notify(src, locale('admin_invalid_player'), Types.NotifyType.ERROR)
        end
        
    elseif action == 'addxp' then
        if not args.target or not args.value then
            Bridge.Notify(src, 'Usage: /mining addxp [playerid] [amount]', Types.NotifyType.ERROR)
            return
        end
        
        local targetSrc = args.target
        local xpAmount = math.max(0, args.value)
        
        if MiningData.players[targetSrc] then
            exports.qbx_mining:AddXP(targetSrc, xpAmount)
            Bridge.Notify(src, locale('admin_xp_added', xpAmount, Bridge.GetPlayerName(targetSrc)), Types.NotifyType.SUCCESS)
        else
            Bridge.Notify(src, locale('admin_invalid_player'), Types.NotifyType.ERROR)
        end
        
    elseif action == 'regen' then
        TriggerEvent('qbx_mining:server:generateNodes')
        Bridge.Notify(src, locale('admin_nodes_regenerated'), Types.NotifyType.SUCCESS)
        
    elseif action == 'debug' then
        Config.Debug = not Config.Debug
        local status = Config.Debug and 'enabled' or 'disabled'
        Bridge.Notify(src, locale('admin_debug_' .. status), Types.NotifyType.INFO)
        
        -- Sync debug state to all clients
        TriggerClientEvent('qbx_mining:client:setDebug', -1, Config.Debug)
        
    else
        Bridge.Notify(src, 'Available actions: setlevel, addxp, regen, debug', Types.NotifyType.INFO)
    end
end)

-- Exports for other resources
exports('GetPlayerMiningLevel', function(src)
    return MiningData.players[src] and MiningData.players[src].level or 1
end)

exports('AddMiningXP', function(src, amount)
    return exports.qbx_mining:AddXP(src, amount)
end)

exports('GetMiningData', function()
    return MiningData
end)

-- Initialize when resource starts
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        InitializeMining()
    end
end)

-- Cleanup when resource stops
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Save all data before stopping
        exports.qbx_mining:SavePersistentData()
        
        if Config.Debug then
            print('^2[QBX Mining]^7 Resource stopped, data saved.')
        end
    end
end)
