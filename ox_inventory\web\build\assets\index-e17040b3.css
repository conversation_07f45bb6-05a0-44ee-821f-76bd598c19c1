.item-image {
	position: relative;
	z-index: 0;
	width: 100%;
	height: 100%;
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
	pointer-events: none
}

.inventory-slot-label-box {
	background: radial-gradient(circle at center, #2f2f37 10%, #26262d 30%, #22232c 60%, #15151a 90%);
	color: #c1c2c5;
	text-align: center;
	min-height: 1vh;
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
	border-top-color: #0003;
	border-top-style: inset;
	border-top-width: .1px
}

.inventory-slot-label-text {
	text-transform: uppercase;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: .2vh .3vh;
	font-weight: 400;
	font-family: Lexend, sans-serif;
	font-size: 1.1vh;
	text-shadow: 1px 1px 10px rgba(0, 0, 0, .6)
}

.inventory-slot-number {
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #22232c;
	color: #c1c2c5;
	height: 1.2vh;
	min-width: 1.2vh;
	border-right: .1px solid #22232c;
	border-bottom: .1px solid #22232c;
	border-top-left-radius: 5px;
	border-bottom-right-radius: secondRadius;
	padding: .3vh;
	font-size: 1.2vh;
	font-family: Lexend, sans-serif;
	text-shadow: 1px 1px 10px rgba(0, 0, 0, .6);
	-webkit-box-shadow: 1px 2px 15px 2px rgba(0, 0, 0, .54);
	box-shadow: 1px 2px 15px 2px #0000008a
}

@media only screen and (min-height: 2160px) {
	.inventory-slot-number {
		border-top-left-radius: 10px;
		border-bottom-right-radius: 10px
	}
}

.inventory-slot,
.item-notification-item-box,
.hotbar-item-slot {
	background-color: #0c0c0c66;
	background-repeat: no-repeat;
	background-position: center;
	border-radius: 6px;
	image-rendering: -webkit-optimize-contrast;
	position: relative;
	background-size: cover;
	color: #c1c2c5;
	border: .1px solid #2d2f3a;
	background-image: url(https://cdn.frvgs.com/assets/custom/default.png)
}

.inventory-slot:hover,
.item-notification-item-box:hover,
.hotbar-item-slot:hover {
	transform: scale(1.03);
	background-color: #803700;
	cursor: pointer;
	border: .1px solid #E66E00;
	box-shadow: 0 0 6px #CC6000;
	transition: all .3s ease
}

.inventory-slot.inventory-slot-chemise,
.inventory-slot-chemise.item-notification-item-box,
.inventory-slot-chemise.hotbar-item-slot,
.inventory-slot.inventory-slot-hoodie,
.inventory-slot-hoodie.item-notification-item-box,
.inventory-slot-hoodie.hotbar-item-slot,
.inventory-slot.inventory-slot-hoodie2,
.inventory-slot-hoodie2.item-notification-item-box,
.inventory-slot-hoodie2.hotbar-item-slot,
.inventory-slot.inventory-slot-hoodie3,
.inventory-slot-hoodie3.item-notification-item-box,
.inventory-slot-hoodie3.hotbar-item-slot,
.inventory-slot.inventory-slot-pants1,
.inventory-slot-pants1.item-notification-item-box,
.inventory-slot-pants1.hotbar-item-slot,
.inventory-slot.inventory-slot-pants2,
.inventory-slot-pants2.item-notification-item-box,
.inventory-slot-pants2.hotbar-item-slot,
.inventory-slot.inventory-slot-pants3,
.inventory-slot-pants3.item-notification-item-box,
.inventory-slot-pants3.hotbar-item-slot,
.inventory-slot.inventory-slot-shoes2,
.inventory-slot-shoes2.item-notification-item-box,
.inventory-slot-shoes2.hotbar-item-slot,
.inventory-slot.inventory-slot-sneakers,
.inventory-slot-sneakers.item-notification-item-box,
.inventory-slot-sneakers.hotbar-item-slot,
.inventory-slot.inventory-slot-jeans,
.inventory-slot-jeans.item-notification-item-box,
.inventory-slot-jeans.hotbar-item-slot {
	background-color: #e91e634d
}

.inventory-slot.inventory-slot-bracelet,
.inventory-slot-bracelet.item-notification-item-box,
.inventory-slot-bracelet.hotbar-item-slot,
.inventory-slot.inventory-slot-bracelet2,
.inventory-slot-bracelet2.item-notification-item-box,
.inventory-slot-bracelet2.hotbar-item-slot,
.inventory-slot.inventory-slot-necklace1,
.inventory-slot-necklace1.item-notification-item-box,
.inventory-slot-necklace1.hotbar-item-slot,
.inventory-slot.inventory-slot-watch,
.inventory-slot-watch.item-notification-item-box,
.inventory-slot-watch.hotbar-item-slot {
	background-color: #3f51b54d
}

.inventory-slot.inventory-slot-bandage,
.inventory-slot-bandage.item-notification-item-box,
.inventory-slot-bandage.hotbar-item-slot,
.inventory-slot.inventory-slot-medikit,
.inventory-slot-medikit.item-notification-item-box,
.inventory-slot-medikit.hotbar-item-slot,
.inventory-slot.inventory-slot-medikit2,
.inventory-slot-medikit2.item-notification-item-box,
.inventory-slot-medikit2.hotbar-item-slot {
	background-color: #4caf504d
}

.inventory-slot.inventory-slot-money,
.inventory-slot-money.item-notification-item-box,
.inventory-slot-money.hotbar-item-slot,
.inventory-slot.inventory-slot-black_money,
.inventory-slot-black_money.item-notification-item-box,
.inventory-slot-black_money.hotbar-item-slot,
.inventory-slot.inventory-slot-coin,
.inventory-slot-coin.item-notification-item-box,
.inventory-slot-coin.hotbar-item-slot,
.inventory-slot.inventory-slot-card_bank,
.inventory-slot-card_bank.item-notification-item-box,
.inventory-slot-card_bank.hotbar-item-slot {
	background-color: #ffc1074d
}

.inventory-slot.inventory-slot-baguette,
.inventory-slot-baguette.item-notification-item-box,
.inventory-slot-baguette.hotbar-item-slot,
.inventory-slot.inventory-slot-bread,
.inventory-slot-bread.item-notification-item-box,
.inventory-slot-bread.hotbar-item-slot,
.inventory-slot.inventory-slot-croissants,
.inventory-slot-croissants.item-notification-item-box,
.inventory-slot-croissants.hotbar-item-slot,
.inventory-slot.inventory-slot-burger,
.inventory-slot-burger.item-notification-item-box,
.inventory-slot-burger.hotbar-item-slot,
.inventory-slot.inventory-slot-burger_chicken,
.inventory-slot-burger_chicken.item-notification-item-box,
.inventory-slot-burger_chicken.hotbar-item-slot,
.inventory-slot.inventory-slot-mustard,
.inventory-slot-mustard.item-notification-item-box,
.inventory-slot-mustard.hotbar-item-slot,
.inventory-slot.inventory-slot-ketchup,
.inventory-slot-ketchup.item-notification-item-box,
.inventory-slot-ketchup.hotbar-item-slot,
.inventory-slot.inventory-slot-mayo,
.inventory-slot-mayo.item-notification-item-box,
.inventory-slot-mayo.hotbar-item-slot,
.inventory-slot.inventory-slot-donut,
.inventory-slot-donut.item-notification-item-box,
.inventory-slot-donut.hotbar-item-slot,
.inventory-slot.inventory-slot-fries,
.inventory-slot-fries.item-notification-item-box,
.inventory-slot-fries.hotbar-item-slot,
.inventory-slot.inventory-slot-pizza_slice,
.inventory-slot-pizza_slice.item-notification-item-box,
.inventory-slot-pizza_slice.hotbar-item-slot,
.inventory-slot.inventory-slot-pizza,
.inventory-slot-pizza.item-notification-item-box,
.inventory-slot-pizza.hotbar-item-slot,
.inventory-slot.inventory-slot-chips,
.inventory-slot-chips.item-notification-item-box,
.inventory-slot-chips.hotbar-item-slot,
.inventory-slot.inventory-slot-tomato,
.inventory-slot-tomato.item-notification-item-box,
.inventory-slot-tomato.hotbar-item-slot {
	background-color: #ff98004d
}

.inventory-slot.inventory-slot-bottle_black,
.inventory-slot-bottle_black.item-notification-item-box,
.inventory-slot-bottle_black.hotbar-item-slot,
.inventory-slot.inventory-slot-bottle_gold,
.inventory-slot-bottle_gold.item-notification-item-box,
.inventory-slot-bottle_gold.hotbar-item-slot,
.inventory-slot.inventory-slot-bottle_empty,
.inventory-slot-bottle_empty.item-notification-item-box,
.inventory-slot-bottle_empty.hotbar-item-slot,
.inventory-slot.inventory-slot-bottle_silver,
.inventory-slot-bottle_silver.item-notification-item-box,
.inventory-slot-bottle_silver.hotbar-item-slot {
	background-color: #03a9f44d
}

.inventory-slot.inventory-slot-potion,
.inventory-slot-potion.item-notification-item-box,
.inventory-slot-potion.hotbar-item-slot,
.inventory-slot.inventory-slot-water,
.inventory-slot-water.item-notification-item-box,
.inventory-slot-water.hotbar-item-slot,
.inventory-slot.inventory-slot-water2,
.inventory-slot-water2.item-notification-item-box,
.inventory-slot-water2.hotbar-item-slot,
.inventory-slot.inventory-slot-milk,
.inventory-slot-milk.item-notification-item-box,
.inventory-slot-milk.hotbar-item-slot,
.inventory-slot.inventory-slot-beer,
.inventory-slot-beer.item-notification-item-box,
.inventory-slot-beer.hotbar-item-slot,
.inventory-slot.inventory-slot-beer2,
.inventory-slot-beer2.item-notification-item-box,
.inventory-slot-beer2.hotbar-item-slot,
.inventory-slot.inventory-slot-cola,
.inventory-slot-cola.item-notification-item-box,
.inventory-slot-cola.hotbar-item-slot,
.inventory-slot.inventory-slot-sprunk,
.inventory-slot-sprunk.item-notification-item-box,
.inventory-slot-sprunk.hotbar-item-slot,
.inventory-slot.inventory-slot-juice_orange,
.inventory-slot-juice_orange.item-notification-item-box,
.inventory-slot-juice_orange.hotbar-item-slot,
.inventory-slot.inventory-slot-juice_lemon,
.inventory-slot-juice_lemon.item-notification-item-box,
.inventory-slot-juice_lemon.hotbar-item-slot,
.inventory-slot.inventory-slot-juice_tea,
.inventory-slot-juice_tea.item-notification-item-box,
.inventory-slot-juice_tea.hotbar-item-slot {
	background-color: #00bcd44d
}

.inventory-slot.inventory-slot-gold,
.inventory-slot-gold.item-notification-item-box,
.inventory-slot-gold.hotbar-item-slot,
.inventory-slot.inventory-slot-iron,
.inventory-slot-iron.item-notification-item-box,
.inventory-slot-iron.hotbar-item-slot,
.inventory-slot.inventory-slot-gem,
.inventory-slot-gem.item-notification-item-box,
.inventory-slot-gem.hotbar-item-slot,
.inventory-slot.inventory-slot-diamond,
.inventory-slot-diamond.item-notification-item-box,
.inventory-slot-diamond.hotbar-item-slot,
.inventory-slot.inventory-slot-stone,
.inventory-slot-stone.item-notification-item-box,
.inventory-slot-stone.hotbar-item-slot,
.inventory-slot.inventory-slot-wood,
.inventory-slot-wood.item-notification-item-box,
.inventory-slot-wood.hotbar-item-slot {
	background-color: #8bc34a4d
}

.inventory-slot.inventory-slot-phone,
.inventory-slot-phone.item-notification-item-box,
.inventory-slot-phone.hotbar-item-slot,
.inventory-slot.inventory-slot-usb_black,
.inventory-slot-usb_black.item-notification-item-box,
.inventory-slot-usb_black.hotbar-item-slot,
.inventory-slot.inventory-slot-tablet,
.inventory-slot-tablet.item-notification-item-box,
.inventory-slot-tablet.hotbar-item-slot,
.inventory-slot.inventory-slot-pc_gaming,
.inventory-slot-pc_gaming.item-notification-item-box,
.inventory-slot-pc_gaming.hotbar-item-slot,
.inventory-slot.inventory-slot-pc_laptop,
.inventory-slot-pc_laptop.item-notification-item-box,
.inventory-slot-pc_laptop.hotbar-item-slot,
.inventory-slot.inventory-slot-radio,
.inventory-slot-radio.item-notification-item-box,
.inventory-slot-radio.hotbar-item-slot,
.inventory-slot.inventory-slot-boombox,
.inventory-slot-boombox.item-notification-item-box,
.inventory-slot-boombox.hotbar-item-slot,
.inventory-slot.inventory-slot-radiobox,
.inventory-slot-radiobox.item-notification-item-box,
.inventory-slot-radiobox.hotbar-item-slot {
	background-color: #9c27b04d
}

.inventory-slot.inventory-slot-lockpick,
.inventory-slot-lockpick.item-notification-item-box,
.inventory-slot-lockpick.hotbar-item-slot,
.inventory-slot.inventory-slot-carkey,
.inventory-slot-carkey.item-notification-item-box,
.inventory-slot-carkey.hotbar-item-slot,
.inventory-slot.inventory-slot-key,
.inventory-slot-key.item-notification-item-box,
.inventory-slot-key.hotbar-item-slot,
.inventory-slot.inventory-slot-paperbag,
.inventory-slot-paperbag.item-notification-item-box,
.inventory-slot-paperbag.hotbar-item-slot,
.inventory-slot.inventory-slot-notepad,
.inventory-slot-notepad.item-notification-item-box,
.inventory-slot-notepad.hotbar-item-slot,
.inventory-slot.inventory-slot-garbage,
.inventory-slot-garbage.item-notification-item-box,
.inventory-slot-garbage.hotbar-item-slot {
	background-color: #ff57224d
}

.inventory-slot.inventory-slot-coca,
.inventory-slot-coca.item-notification-item-box,
.inventory-slot-coca.hotbar-item-slot,
.inventory-slot.inventory-slot-weed,
.inventory-slot-weed.item-notification-item-box,
.inventory-slot-weed.hotbar-item-slot {
	background-color: #f443364d
}

.inventory-slot.inventory-slot-armour,
.inventory-slot-armour.item-notification-item-box,
.inventory-slot-armour.hotbar-item-slot,
.inventory-slot.inventory-slot-armour2,
.inventory-slot-armour2.item-notification-item-box,
.inventory-slot-armour2.hotbar-item-slot,
.inventory-slot.inventory-slot-armour3,
.inventory-slot-armour3.item-notification-item-box,
.inventory-slot-armour3.hotbar-item-slot,
.inventory-slot.inventory-slot-cosmo,
.inventory-slot-cosmo.item-notification-item-box,
.inventory-slot-cosmo.hotbar-item-slot {
	background-color: #607d8b4d
}

.container {
	position: absolute;
	transform: translate(-50%, -50%);
	top: -25%;
	left: 50%;
	z-index: 0
}

.glitch,
.glow {
	color: #dfbfbf;
	position: relative;
	font-size: 5vw;
	animation: glitch 5s 5s infinite;
	font-family: Lexend, sans-serif
}

.glitch:before,
.glow:before {
	content: attr(data-text);
	position: absolute;
	left: -2px;
	text-shadow: -5px 0 #E66E00;
	background: transparent;
	overflow: hidden;
	top: 0;
	animation: noise-1 3s linear infinite alternate-reverse, glitch 5s 5.05s infinite
}

.glitch:after,
.glow:after {
	content: attr(data-text);
	position: absolute;
	left: 2px;
	text-shadow: -5px 0 lightgreen;
	background: transparent;
	overflow: hidden;
	top: 0;
	animation: noise-2 3s linear infinite alternate-reverse, glitch 5s 5s infinite
}

@keyframes noise-1 {
	3.3333333333% {
		clip-path: inset(40px 0 25px 0)
	}

	6.6666666667% {
		clip-path: inset(44px 0 43px 0)
	}

	10% {
		clip-path: inset(45px 0 49px 0)
	}

	13.3333333333% {
		clip-path: inset(46px 0 14px 0)
	}

	16.6666666667% {
		clip-path: inset(54px 0 17px 0)
	}

	20% {
		clip-path: inset(68px 0 20px 0)
	}

	23.3333333333% {
		clip-path: inset(30px 0 44px 0)
	}

	26.6666666667% {
		clip-path: inset(25px 0 15px 0)
	}

	30% {
		clip-path: inset(47px 0 21px 0)
	}

	33.3333333333% {
		clip-path: inset(10px 0 49px 0)
	}

	36.6666666667% {
		clip-path: inset(3px 0 27px 0)
	}

	40% {
		clip-path: inset(52px 0 32px 0)
	}

	43.3333333333% {
		clip-path: inset(27px 0 11px 0)
	}

	46.6666666667% {
		clip-path: inset(42px 0 30px 0)
	}

	50% {
		clip-path: inset(43px 0 42px 0)
	}

	53.3333333333% {
		clip-path: inset(44px 0 10px 0)
	}

	56.6666666667% {
		clip-path: inset(5px 0 41px 0)
	}

	60% {
		clip-path: inset(12px 0 44px 0)
	}

	63.3333333333% {
		clip-path: inset(82px 0 7px 0)
	}

	66.6666666667% {
		clip-path: inset(26px 0 32px 0)
	}

	70% {
		clip-path: inset(21px 0 23px 0)
	}

	73.3333333333% {
		clip-path: inset(13px 0 71px 0)
	}

	76.6666666667% {
		clip-path: inset(30px 0 50px 0)
	}

	80% {
		clip-path: inset(75px 0 5px 0)
	}

	83.3333333333% {
		clip-path: inset(52px 0 9px 0)
	}

	86.6666666667% {
		clip-path: inset(78px 0 4px 0)
	}

	90% {
		clip-path: inset(27px 0 65px 0)
	}

	93.3333333333% {
		clip-path: inset(65px 0 5px 0)
	}

	96.6666666667% {
		clip-path: inset(15px 0 40px 0)
	}

	to {
		clip-path: inset(83px 0 9px 0)
	}
}

@keyframes noise-2 {
	0% {
		clip-path: inset(16px 0 30px 0)
	}

	3.3333333333% {
		clip-path: inset(74px 0 5px 0)
	}

	6.6666666667% {
		clip-path: inset(79px 0 11px 0)
	}

	10% {
		clip-path: inset(90px 0 8px 0)
	}

	13.3333333333% {
		clip-path: inset(46px 0 29px 0)
	}

	16.6666666667% {
		clip-path: inset(24px 0 17px 0)
	}

	20% {
		clip-path: inset(76px 0 15px 0)
	}

	23.3333333333% {
		clip-path: inset(76px 0 13px 0)
	}

	26.6666666667% {
		clip-path: inset(24px 0 60px 0)
	}

	30% {
		clip-path: inset(42px 0 32px 0)
	}

	33.3333333333% {
		clip-path: inset(1px 0 64px 0)
	}

	36.6666666667% {
		clip-path: inset(91px 0 10px 0)
	}

	40% {
		clip-path: inset(26px 0 21px 0)
	}

	43.3333333333% {
		clip-path: inset(62px 0 13px 0)
	}

	46.6666666667% {
		clip-path: inset(7px 0 77px 0)
	}

	50% {
		clip-path: inset(14px 0 40px 0)
	}

	53.3333333333% {
		clip-path: inset(3px 0 38px 0)
	}

	56.6666666667% {
		clip-path: inset(46px 0 21px 0)
	}

	60% {
		clip-path: inset(58px 0 9px 0)
	}

	63.3333333333% {
		clip-path: inset(12px 0 20px 0)
	}

	66.6666666667% {
		clip-path: inset(42px 0 3px 0)
	}

	70% {
		clip-path: inset(12px 0 6px 0)
	}

	73.3333333333% {
		clip-path: inset(15px 0 31px 0)
	}

	76.6666666667% {
		clip-path: inset(87px 0 8px 0)
	}

	80% {
		clip-path: inset(98px 0 2px 0)
	}

	83.3333333333% {
		clip-path: inset(91px 0 1px 0)
	}

	86.6666666667% {
		clip-path: inset(35px 0 22px 0)
	}

	90% {
		clip-path: inset(36px 0 35px 0)
	}

	93.3333333333% {
		clip-path: inset(77px 0 24px 0)
	}

	96.6666666667% {
		clip-path: inset(23px 0 73px 0)
	}

	to {
		clip-path: inset(9px 0 14px 0)
	}
}

.glow {
	text-shadow: 0 0 1000px #dfbfbf;
	color: transparent;
	position: absolute;
	top: 0
}

.subtitle {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: 100;
	font-size: 1vw;
	color: #fff6;
	text-transform: uppercase;
	letter-spacing: .5em;
	text-align: center;
	position: absolute;
	left: 17%;
	animation: glitch-2 5s 5.02s infinite;
	text-shadow: .1vh .1vh 0 rgba(255, 255, 255, .7)
}

@keyframes glitch-2 {
	1% {
		transform: rotateX(10deg) skew(70deg)
	}

	2% {
		transform: rotateX(0) skew(0)
	}
}

@media only screen and (min-height: 2160px) {

	.glitch:before,
	.glow:before {
		content: attr(data-text);
		position: absolute;
		left: -4px;
		text-shadow: -10px 0 #E66E00;
		background: transparent;
		overflow: hidden;
		top: 0;
		animation: noise-1 3s linear infinite alternate-reverse, glitch 5s 5.05s infinite
	}

	.glitch:after,
	.glow:after {
		content: attr(data-text);
		position: absolute;
		left: 4px;
		text-shadow: -10px 0 lightgreen;
		background: transparent;
		overflow: hidden;
		top: 0;
		animation: noise-2 3s linear infinite alternate-reverse, glitch 5s 5s infinite
	}

	.glow {
		text-shadow: 0 0 1000px #dfbfbf;
		color: transparent;
		position: absolute;
		top: 0
	}

	.subtitle {
		font-family: Arial, Helvetica, sans-serif;
		font-weight: 100;
		font-size: 1vw;
		color: #fff6;
		text-transform: uppercase;
		letter-spacing: .5em;
		text-align: center;
		position: absolute;
		left: 17%;
		animation: glitch-2 5s 5.02s infinite;
		text-shadow: .1vh .1vh 0 rgba(255, 255, 255, .7)
	}
}

.inventory-control-button {
	font-size: 1rem;
	color: #fff;
	background-color: #0c0c0c66;
	transition: .2s;
	border-radius: 6px;
	border: none;
	text-transform: uppercase;
	font-family: Lexend, sans-serif;
	width: 100%;
	font-weight: 500;
	box-shadow: 0 0 13px #000000bf;
	background: linear-gradient(to right, #CC6000, #FE7C00, #FF944D, #E66E00, #CC6000, #CC6000);
	padding: .1rem;
	transition: all .3s ease
}

.inventory-control-button:hover {
	animation: rainbow 22s infinite linear alternate;
	transform: scale(1.03)
}

.inventory-control-buttonTxt {
	background: rgba(0, 0, 0, .85);
	border-radius: 5px;
	box-shadow: 0 1px 1px #0000004d, 0 2px 2px #00000014, 0 4px 4px #0000000f, 0 8px 8px #0000000a, 0 16px 16px #00000005;
	min-width: 100%;
	padding-top: .85rem;
	padding-bottom: .85rem
}

.inventory-control-buttonTxt:hover {
	background: rgba(0, 0, 0, .75);
	box-shadow: 0 1px 1px #0000004d, 0 2px 2px #00000014, 0 4px 4px #0000000f, 0 8px 8px #0000000a, 0 16px 16px #00000005;
	min-width: 100%;
	padding-top: .85rem;
	padding-bottom: .85rem
}

@keyframes rainbow {
	to {
		background-position-x: 4000px
	}
}

.inventory-control-input-WR {
	border-radius: 6px;
	background: linear-gradient(to right, #CC6000, #FE7C00, #FF944D, #E66E00, #CC6000, #CC6000)
}

.inventory-control-input {
	transition: .2s;
	padding: .8rem;
	border-radius: 5px;
	font-family: Lexend, sans-serif;
	background-color: #121212;
	font-size: 1rem;
	text-align: center;
	outline: none;
	border: none;
	color: #fff;
	margin: .1rem
}

.inventory-control-input:focus-within {
	background-color: #0c0c0ccc
}

@media only screen and (min-height: 2160px) {
	.inventory-control-button {
		border-radius: 12px;
		font-size: 2rem;
		padding: .14rem
	}

	.inventory-control-buttonTxt {
		border-radius: 10px;
		padding-top: 1.7rem;
		padding-bottom: 1.7rem
	}

	.inventory-control-buttonTxt:hover {
		padding-top: 1.7rem;
		padding-bottom: 1.7rem
	}

	.inventory-control-separator {
		min-height: 120px
	}

	.inventory-control-input-WR {
		border-radius: 12px
	}

	.inventory-control-input {
		border-radius: 10px;
		padding: 1.6rem;
		font-size: 2rem;
		margin: .14rem
	}
}

body {
	margin: 0;
	font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Lexend, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	height: 100vh;
	background: none !important;
	overflow: hidden !important;
	user-select: none
}

#root {
	height: 100%
}

code {
	font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace
}

::-webkit-scrollbar {
	display: none
}

p {
	margin: 0;
	padding: 0;
	font-family: Lexend, sans-serif
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none
}

.InventoryLogoSVG {
	fill: #FF944D
}

.InventoryLogo {
	padding-left: 0px;
	padding-right: 0px;
	margin-bottom: -40px;
	transform: translateY(-100px)
	
}

.app-wrapper {
	height: 100%;
	width: 100%;
	color: #fff
}

.context-menu-list {
	min-width: 100px;
	background-color: #22232c;
	color: #c1c2c5;
	padding: 4px;
	border-color: #0003;
	border-style: inset;
	border-width: 1px;
	border-radius: 6px;
	outline: none;
	display: flex;
	flex-direction: column;
	border: .1px solid #FF944D
}

.context-menu-item {
	font-size: .9rem;
	padding: 8px;
	border-radius: 6px;
	background-color: transparent;
	outline: none;
	border: none;
	color: #c1c2c5;
	display: flex;
	justify-content: space-between;
	align-items: center
}

.context-menu-item:active {
	transform: none
}

.context-menu-item:hover {
	background-color: #33343f;
	cursor: pointer
}

.tooltip-description {
	padding-top: 5px
}

.tooltip-markdown>p {
	margin: 0
}

button:active {
	transform: translateY(3px)
}

.item-drag-preview {
	width: 7.7vh;
	height: 7.7vh;
	z-index: 1;
	position: fixed;
	pointer-events: none;
	top: 0;
	left: 0;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 7vh;
	image-rendering: -webkit-optimize-contrast
}

.inventory-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	height: 100%;
	gap: 20px
}

.inventory-control {
	display: flex
}

.inventory-control .inventory-control-wrapper {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
	justify-content: center;
	align-items: center
}

.useful-controls-dialog {
	background-color: #22232c;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #c1c2c5;
	min-width: 50vw;
	min-height: 50vh;
	display: flex;
	flex-direction: column;
	padding: 3px;
	border-radius: 6px;
	background: linear-gradient(to right, #CC6000, #FE7C00, #FF944D, #E66E00, #CC6000, #CC6000);
	animation: rainbow 22s infinite linear alternate;
	gap: 16px
}

.useful-controls-dialog-WR {
	background: #121212;
	min-height: 50vh;
	padding: 1vh;
	border-radius: 5px
}

.useful-controls-dialog-overlay {
	background-color: #00000080
}

.useful-controls-dialog-title {
	display: flex;
	width: 100%;
	justify-content: space-between;
	align-items: center;
	font-size: 1.6rem;
	margin-left: .3rem
}

.useful-controls-dialog-close {
	width: 25px;
	height: 25px;
	padding: 6px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 6px;
	fill: #c1c2c5
}

.useful-controls-dialog-close:hover {
	background-color: #33343f;
	cursor: pointer
}

.useful-controls-content-wrapper {
	display: flex;
	flex-direction: column;
	padding: 10px;
	gap: 20px
}

.useful-controls-content-wrapper p {
	font-size: .8rem
}

.divider {
	width: 100%;
	height: 1px;
	background-color: #ffffff1f
}

.useful-controls-button {
	position: absolute !important;
	bottom: 25px;
	right: 25px;
	transition: .2s !important;
	border: none;
	color: #fff;
	width: 52px;
	height: 52px;
	display: flex;
	justify-content: center;
	align-items: center;
	fill: #fff;
	border-radius: 5% !important;
	background-color: #0c0c0c66 !important
}

.useful-controls-button:hover {
	background-color: #0c0c0ccc !important;
	cursor: pointer
}

.useful-controls-exit-button {
	position: absolute !important;
	right: 8px;
	top: 8px;
	border-radius: 6px !important;
	color: gray !important
}

.inventory-grid-wrapper {
	display: flex;
	flex-direction: column;
	gap: 16px
}

.inventory-grid-header-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	margin-bottom: -25px;
	margin-left: 10px;
	margin-right: 10px;
	padding-left: 10px;
	padding-right: 10px
}

.inventory-grid-header-wrapper p {
	font-size: 1rem
}

.inventory-grid-container {
	display: grid;
	height: calc(52.1vh + 40px);
	grid-template-columns: repeat(5, 10.2vh);
	grid-auto-rows: 10.42vh;
	gap: 8px;
	overflow-y: scroll;
	padding-top: 7px;
	padding-left: 7px;
	padding-right: 7px
}

.item-slot-wrapper {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%
}

.item-slot-wrapper p {
	font-size: .9rem
}

.item-slot-header-wrapper,
.item-hotslot-header-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: flex-end
}

.item-hotslot-header-wrapper {
	justify-content: space-between !important
}

.item-slot-info-wrapper {
	display: flex;
	flex-direction: row;
	align-self: flex-end;
	padding: 3px;
	gap: 3px
}

.item-slot-info-wrapper p {
	font-size: .7rem
}

.item-slot-info-wrapper span {
	font-size: .7rem;
	font-weight: 600;
	color: #FF944D
}

.item-slot-currency-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	align-items: center;
	padding-right: 3px
}

.item-slot-currency-wrapper p {
	font-size: .7rem;
	text-shadow: .1vh .1vh 0 rgba(0, 0, 0, .7)
}

.item-slot-price-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	padding-right: 3px
}

.item-slot-price-wrapper p {
	font-size: .7rem;
	text-shadow: .1vh .1vh 0 rgba(0, 0, 0, .7)
}

.tooltip-wrapper {
	pointer-events: none;
	display: flex;
	background-color: #22232c;
	width: 200px;
	padding: 8px;
	flex-direction: column;
	min-width: 200px;
	color: #c1c2c5;
	font-family: Lexend, sans-serif;
	border-radius: 6px;
	border-color: #0003;
	border-style: inset;
	border-width: 1px
}

.tooltip-wrapper p {
	font-size: .8rem;
	font-weight: 400
}

.tooltip-header-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center
}

.tooltip-header-wrapper p {
	font-size: 1rem;
	font-weight: 400
}

.tooltip-crafting-duration {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center
}

.tooltip-crafting-duration svg {
	padding-right: 3px
}

.tooltip-crafting-duration p {
	font-size: 1rem
}

.tooltip-ingredients {
	padding-top: 5px
}

.tooltip-ingredient {
	display: flex;
	flex-direction: row;
	align-items: center
}

.tooltip-ingredient img {
	width: 28px;
	height: 28px;
	padding-right: 5px
}

.hotbar-container {
	display: flex;
	align-items: center;
	gap: 2px;
	justify-content: center;
	width: 100%;
	position: absolute;
	bottom: 2vh
}

.hotbar-item-slot {
	width: 10.2vh;
	height: 10.2vh
}

.hotbar-slot-header-wrapper {
	display: flex;
	flex-direction: row;
	justify-content: space-between
}

.item-notification-container {
	display: flex;
	overflow-x: scroll;
	flex-wrap: nowrap;
	gap: 2px;
	position: absolute;
	bottom: 20vh;
	left: 50%;
	width: 100%;
	margin-left: calc(50% - 5.1vh);
	transform: translate(-50%)
}

.item-notification-action-box {
	width: 100%;
	color: #c1c2c5;
	background-color: #0c0c0c66;
	text-transform: uppercase;
	text-align: center;
	border-top-left-radius: .25vh;
	border-top-right-radius: .25vh;
	font-family: Lexend, sans-serif
}

.item-notification-action-box p {
	font-size: .8rem;
	padding: 2px;
	font-weight: 600
}

.item-notification-item-box {
	height: 10.2vh;
	width: 10.2vh
}

.weight-bar-wrapper {
	padding-left: 7px;
	padding-right: 7px
}

.weight-bar-WR {
	border-radius: 6px;
	padding: 1px;
	background: linear-gradient(to right, #CC6000, #FE7C00, #FF944D, #E66E00, #CC6000, #CC6000);
	margin-bottom: 10px;
	animation: rainbow 22s infinite linear alternate
}



.weight-bar {
	background: rgba(0, 0, 0, .8);
	border: 1px inset rgba(0, 0, 0, .1);
	height: 2vh;
	border-radius: 5px;
	overflow: hidden;
}


.durability-bar {
	background: rgba(0, 0, 0, .5);
	height: 3px;
	overflow: hidden
}

.transition-fade-enter {
	opacity: 0
}

.transition-fade-enter-active {
	opacity: 1;
	transition: opacity .2s
}

.transition-fade-exit {
	opacity: 1
}

.transition-fade-exit-active {
	opacity: 0;
	transition: opacity .2s
}

.transition-slide-up-enter {
	transform: translateY(200px)
}

.transition-slide-up-enter-active {
	transform: translateY(0);
	transition: all .2s
}

.transition-slide-up-exit {
	transform: translateY(0)
}

.transition-slide-up-exit-active {
	transform: translateY(200px);
	transition: all .2s
}

.PanelClose {
	width: 20px;
	height: 20px
}

@media only screen and (min-height: 2160px) {
	.InventoryLogo {
		padding-left: 20px;
		padding-right: 20px;
		margin-bottom: 30px
	}

	.app-wrapper {
		height: 100%;
		width: 100%;
		color: #fff
	}

	.context-menu-list {
		min-width: 200px;
		padding: 8px;
		border-width: 2px;
		border-radius: 12px;
		border: .2px solid #FF944D
	}

	.context-menu-item {
		border-radius: 12px;
		font-size: 2rem;
		padding: 16px
	}

	.tooltip-description {
		padding-top: 10px
	}

	.tooltip-markdown>p {
		margin: 0
	}

	.item-drag-preview {
		width: 7.7vh;
		height: 7.7vh;
		background-size: 7vh
	}

	.inventory-wrapper {
		gap: 20px
	}

	.inventory-control-wrapper {
		display: flex;
		flex-direction: column;
		gap: 2.5rem;
		justify-content: center;
		align-items: center
	}

	.useful-controls-dialog {
		background-color: #22232c;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: #c1c2c5;
		min-width: 50vw;
		min-height: 50vh;
		display: flex;
		flex-direction: column;
		padding: 6px;
		border-radius: 12px;
		background: linear-gradient(to right, #CC6000, #FE7C00, #FF944D, #E66E00, #CC6000, #CC6000);
		animation: rainbow 22s infinite linear alternate;
		gap: 22px
	}

	.useful-controls-dialog-WR {
		background: #121212;
		min-height: 50vh;
		padding: 1vh;
		border-radius: 5px
	}

	.useful-controls-dialog-overlay {
		background-color: #00000080
	}

	.useful-controls-dialog-title {
		display: flex;
		width: 100%;
		justify-content: space-between;
		align-items: center;
		font-size: 2.6rem;
		margin-left: .3rem
	}

	.useful-controls-dialog-close {
		width: 50px;
		height: 50px;
		padding: 12px;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 12px;
		fill: #c1c2c5
	}

	.useful-controls-dialog-close:hover {
		background-color: #33343f;
		cursor: pointer
	}

	.useful-controls-content-wrapper {
		display: flex;
		flex-direction: column;
		padding: 10px;
		gap: 30px
	}

	.useful-controls-content-wrapper p {
		font-size: 1.8rem
	}

	.divider {
		width: 100%;
		height: 1px;
		background-color: #ffffff1f
	}

	.useful-controls-button {
		position: absolute !important;
		bottom: 25px;
		right: 25px;
		transition: .2s !important;
		border: none;
		color: #fff;
		width: 52px;
		height: 52px;
		display: flex;
		justify-content: center;
		align-items: center;
		fill: #fff;
		border-radius: 5% !important;
		background-color: #0c0c0c66 !important
	}

	.useful-controls-button:hover {
		background-color: #0c0c0ccc !important;
		cursor: pointer
	}

	.useful-controls-exit-button {
		position: absolute !important;
		right: 8px;
		top: 8px;
		border-radius: 12px !important;
		color: gray !important
	}

	.inventory-grid-wrapper {
		display: flex;
		flex-direction: column;
		gap: 16px
	}

	.inventory-grid-header-wrapper {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		margin-bottom: -50px;
		margin-left: 20px;
		margin-right: 20px;
		padding-left: 20px;
		padding-right: 20px
	}

	.inventory-grid-header-wrapper p {
		font-size: 2rem
	}

	.inventory-grid-container {
		display: grid;
		height: calc(52.1vh + 40px);
		grid-template-columns: repeat(5, 10.2vh);
		grid-auto-rows: 10.42vh;
		gap: 8px;
		overflow-y: scroll;
		padding-left: 7px;
		padding-right: 7px
	}

	.item-slot-wrapper {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 100%
	}

	.item-slot-wrapper p {
		font-size: 1.8rem
	}

	.item-slot-info-wrapper {
		padding: 9px;
		gap: 12px
	}

	.item-slot-info-wrapper p {
		font-size: 1.2rem
	}

	.item-slot-info-wrapper span {
		font-size: 1.2rem;
		font-weight: 700;
		color: #E66E00
	}

	.item-slot-currency-wrapper {
		padding-right: 3px
	}

	.item-slot-currency-wrapper p {
		font-size: 1.7rem;
		text-shadow: .1vh .1vh 0 rgba(0, 0, 0, .7)
	}

	.item-slot-price-wrapper {
		padding-right: 3px
	}

	.item-slot-price-wrapper p {
		font-size: 1.7rem
	}

	.tooltip-wrapper {
		width: 200px;
		padding: 8px;
		border-width: 1px
	}

	.tooltip-wrapper p {
		font-size: 1.6rem;
		font-weight: 400
	}

	.tooltip-header-wrapper {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center
	}

	.tooltip-header-wrapper p {
		font-size: 2rem;
		font-weight: 400
	}

	.tooltip-crafting-duration {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center
	}

	.tooltip-crafting-duration svg {
		padding-right: 3px
	}

	.tooltip-crafting-duration p {
		font-size: 2rem
	}

	.tooltip-ingredients {
		padding-top: 5px
	}

	.tooltip-ingredient {
		display: flex;
		flex-direction: row;
		align-items: center
	}

	.tooltip-ingredient img {
		width: 28px;
		height: 28px;
		padding-right: 5px
	}

	.hotbar-container {
		display: flex;
		align-items: center;
		gap: 2px;
		justify-content: center;
		width: 100%;
		position: absolute;
		bottom: 2vh
	}

	.item-notification-container {
		gap: 2px;
		position: absolute;
		bottom: 20vh;
		left: 50%;
		width: 100%;
		margin-left: calc(50% - 5.1vh);
		transform: translate(-50%)
	}

	.item-notification-action-box {
		width: 100%;
		border-top-left-radius: .25vh;
		border-top-right-radius: .25vh
	}

	.item-notification-action-box p {
		font-size: 1.6rem;
		padding: 4px;
		font-weight: 600
	}

	.weight-bar-wrapper {
		padding-left: 7px;
		padding-right: 7px
	}

	.weight-bar-WR {
		border-radius: 12px;
		padding: 2px
	}

	.weight-bar {
		border: 1px inset rgba(0, 0, 0, .1);
		height: 3vh;
		border-radius: 10px
	}

	.durability-bar {
		background: rgba(0, 0, 0, .8);
		height: 3px;
		overflow: hidden
	}

	.PanelClose {
		width: 40px;
		height: 40px
	}
}