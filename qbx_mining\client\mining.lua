-- QBX Mining System - Mining Client Logic
local locale = lib.locale

-- Mining interaction system using ox_target
local function SetupNodeTargeting()
    -- For now, disable ox_target integration and use simple interaction
    -- This can be re-enabled once the targeting system is properly configured
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Node targeting setup complete (simplified mode)')
    end
end

-- Get nearest node ID to coordinates
function GetNearestNodeId(coords)
    local nearestId = nil
    local nearestDistance = math.huge
    
    for nodeId, node in pairs(MiningNodes) do
        local distance = Types.Utils.GetDistance(coords, node.position)
        if distance < nearestDistance and distance <= Config.Target.Distance then
            nearestDistance = distance
            nearestId = nodeId
        end
    end
    
    return nearestId
end

-- Enhanced mining validation
function ValidateClientMining(nodeId)
    local node = MiningNodes[nodeId]
    if not node then
        return false, 'invalid_node'
    end
    
    -- Check node state
    if node.state ~= Types.NodeState.ACTIVE then
        return false, 'node_not_ready'
    end
    
    -- Check level requirement
    if PlayerLevel.level < node.minLevel then
        return false, 'level_required'
    end
    
    -- Check if already mining
    if CurrentMining.active then
        return false, 'already_mining'
    end
    
    -- Check distance
    local playerCoords = GetEntityCoords(PlayerPedId())
    local distance = Types.Utils.GetDistance(playerCoords, node.position)
    if distance > Config.AntiExploit.DistanceCheck then
        return false, 'too_far'
    end
    
    return true, nil
end

-- Advanced mining animation system
function PlayMiningAnimation(nodeId)
    local node = MiningNodes[nodeId]
    if not node then return false end
    
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    
    -- Calculate facing direction towards node
    local direction = node.position - playerCoords
    local heading = GetHeadingFromVector_2d(direction.x, direction.y)
    SetEntityHeading(playerPed, heading)
    
    -- Load animation dictionary
    local animDict = Config.Animations.Dict
    local animName = Config.Animations.Swing
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    -- Calculate effective animation duration based on tool stats
    local baseDuration = Config.Animations.DurationMs
    local speedMultiplier = GetPlayerToolSpeedMultiplier()
    local effectiveDuration = math.floor(baseDuration / speedMultiplier)
    
    -- Play animation
    TaskPlayAnim(
        playerPed,
        animDict,
        animName,
        8.0, -8.0,
        effectiveDuration,
        1, 0,
        false, false, false
    )
    
    return true, effectiveDuration
end

-- Get player's tool speed multiplier
function GetPlayerToolSpeedMultiplier()
    local multiplier = 1.0
    
    -- This would typically check the player's equipped tools
    -- For now, we'll use a base multiplier
    -- In a full implementation, this would query the server for current tool stats
    
    return multiplier
end

-- Mining skill check system
function PerformMiningSkillCheck(nodeId)
    local node = MiningNodes[nodeId]
    if not node then return false end
    
    -- Determine difficulty based on ore tier
    local oreTier = Types.Utils.GetOreTier(node.oreType)
    local difficulty = {'easy', 'medium', 'hard'}
    local skillLevel = math.min(3, math.ceil(oreTier / 2))
    
    -- Perform skill check
    local success = Bridge.SkillCheck(difficulty[skillLevel], {'w', 'a', 's', 'd'})
    
    if success then
        if Config.Debug then
            print('^2[QBX Mining Client]^7 Skill check passed for ' .. node.oreType)
        end
        return true
    else
        Bridge.Notify(locale('mining_failed'), Types.NotifyType.ERROR)
        return false
    end
end

-- Enhanced mining effects system
function PlayEnhancedMiningEffects(position, oreType)
    local playerPed = PlayerPedId()
    
    -- Particle effects based on ore type
    local particleEffects = {
        [Types.OreType.COAL] = {asset = 'core', effect = 'ent_dst_rocks', scale = 0.8},
        [Types.OreType.COPPER] = {asset = 'core', effect = 'ent_dst_rocks', scale = 1.0},
        [Types.OreType.IRON] = {asset = 'core', effect = 'ent_dst_rocks', scale = 1.2},
        [Types.OreType.SILVER] = {asset = 'core', effect = 'ent_dst_rocks', scale = 1.4},
        [Types.OreType.GOLD] = {asset = 'core', effect = 'ent_dst_rocks', scale = 1.6},
        [Types.OreType.DIAMOND] = {asset = 'core', effect = 'ent_dst_rocks', scale = 2.0}
    }
    
    local effect = particleEffects[oreType] or particleEffects[Types.OreType.COAL]
    
    -- Request and play particle effect
    RequestNamedPtfxAsset(effect.asset)
    while not HasNamedPtfxAssetLoaded(effect.asset) do
        Wait(10)
    end
    
    UseParticleFxAssetNextCall(effect.asset)
    StartParticleFxNonLoopedAtCoord(
        effect.effect,
        position.x, position.y, position.z,
        0.0, 0.0, 0.0,
        effect.scale,
        false, false, false
    )
    
    -- Sound effects
    local soundEffects = {
        [Types.OreType.COAL] = 'Hack_Success',
        [Types.OreType.COPPER] = 'Hack_Success',
        [Types.OreType.IRON] = 'Hack_Success',
        [Types.OreType.SILVER] = 'Hack_Success',
        [Types.OreType.GOLD] = 'Hack_Success',
        [Types.OreType.DIAMOND] = 'Hack_Success'
    }
    
    local soundName = soundEffects[oreType] or 'Hack_Success'
    PlaySoundFromCoord(
        -1, soundName,
        position.x, position.y, position.z,
        Config.Effects.HitSound.dict,
        false, Config.Effects.HitSound.range or 10.0, false
    )
    
    -- Screen shake for higher tier ores
    if Types.Utils.GetOreTier(oreType) >= 4 then
        ShakeGameplayCam('SMALL_EXPLOSION_SHAKE', 0.3)
    end
end

-- Mining progress tracking
local MiningProgress = {
    active = false,
    nodeId = nil,
    startTime = 0,
    expectedDuration = 0,
    hits = 0
}

-- Start enhanced mining process
function StartEnhancedMining(nodeId)
    local valid, error = ValidateClientMining(nodeId)
    if not valid then
        Bridge.Notify(locale(error), Types.NotifyType.ERROR)
        return false
    end
    
    local node = MiningNodes[nodeId]
    
    -- Start mining progress
    MiningProgress.active = true
    MiningProgress.nodeId = nodeId
    MiningProgress.startTime = GetGameTimer()
    MiningProgress.hits = 0
    
    -- Play animation
    local success, duration = PlayMiningAnimation(nodeId)
    if not success then
        StopEnhancedMining()
        return false
    end
    
    MiningProgress.expectedDuration = duration
    
    -- Show progress bar with skill check
    local progressSuccess = lib.progressBar({
        duration = duration,
        label = locale('mining_in_progress'),
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = {
            dict = Config.Animations.Dict,
            clip = Config.Animations.Swing
        }
    })
    
    if progressSuccess then
        -- Perform skill check
        local skillSuccess = PerformMiningSkillCheck(nodeId)
        
        if skillSuccess then
            -- Send mining request to server
            local playerCoords = GetEntityCoords(PlayerPedId())
            TriggerServerEvent(Types.Events.CLIENT_MINE_NODE, nodeId, playerCoords)
            
            -- Play effects
            PlayEnhancedMiningEffects(node.position, node.oreType)
            
            MiningProgress.hits = MiningProgress.hits + 1
        end
    end
    
    StopEnhancedMining()
    return progressSuccess
end

-- Stop enhanced mining
function StopEnhancedMining()
    MiningProgress.active = false
    MiningProgress.nodeId = nil
    MiningProgress.startTime = 0
    MiningProgress.expectedDuration = 0
    MiningProgress.hits = 0
    
    local playerPed = PlayerPedId()
    ClearPedTasks(playerPed)
end

-- Mining statistics tracking
local MiningStats = {
    totalMined = 0,
    sessionStart = 0,
    oresMined = {},
    bestStreak = 0,
    currentStreak = 0
}

-- Update mining statistics
function UpdateMiningStats(oreType, amount)
    MiningStats.totalMined = MiningStats.totalMined + amount
    MiningStats.oresMined[oreType] = (MiningStats.oresMined[oreType] or 0) + amount
    MiningStats.currentStreak = MiningStats.currentStreak + 1
    
    if MiningStats.currentStreak > MiningStats.bestStreak then
        MiningStats.bestStreak = MiningStats.currentStreak
    end
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Stats updated: Total=' .. MiningStats.totalMined .. ', Streak=' .. MiningStats.currentStreak)
    end
end

-- Reset mining streak (on failed mining)
function ResetMiningStreak()
    MiningStats.currentStreak = 0
end

-- Get mining statistics
function GetMiningStats()
    return {
        totalMined = MiningStats.totalMined,
        sessionTime = GetGameTimer() - MiningStats.sessionStart,
        oresMined = MiningStats.oresMined,
        bestStreak = MiningStats.bestStreak,
        currentStreak = MiningStats.currentStreak
    }
end

-- Initialize mining statistics
function InitializeMiningStats()
    MiningStats.sessionStart = GetGameTimer()
    MiningStats.totalMined = 0
    MiningStats.oresMined = {}
    MiningStats.bestStreak = 0
    MiningStats.currentStreak = 0
end

-- Node prediction system (for advanced players)
function PredictNodeRespawn(nodeId)
    local node = MiningNodes[nodeId]
    if not node or node.state ~= Types.NodeState.DEPLETED then
        return nil
    end
    
    if node.respawnTime and node.respawnTime > 0 then
        local currentTime = GetGameTimer()
        local timeRemaining = math.max(0, node.respawnTime - currentTime)
        return math.ceil(timeRemaining / 1000) -- Convert to seconds
    end
    
    return nil
end

-- Setup mining system
function SetupMiningSystem()
    SetupNodeTargeting()
    InitializeMiningStats()
    
    if Config.Debug then
        print('^2[QBX Mining Client]^7 Mining system setup complete')
    end
end

-- Event handlers for mining-specific events
RegisterNetEvent(Types.Events.SERVER_NODE_UPDATE, function(nodeId, nodeData)
    if MiningNodes[nodeId] then
        local oldState = MiningNodes[nodeId].state
        MiningNodes[nodeId] = nodeData
        
        -- Handle state changes
        if oldState ~= nodeData.state then
            if nodeData.state == Types.NodeState.DEPLETED then
                -- Node was depleted
                if CurrentMining.nodeId == nodeId then
                    StopEnhancedMining()
                end
            elseif nodeData.state == Types.NodeState.ACTIVE and oldState == Types.NodeState.DEPLETED then
                -- Node respawned
                if Config.Debug then
                    print('^2[QBX Mining Client]^7 Node ' .. nodeId .. ' respawned')
                end
            end
        end
    end
end)

-- Exports
exports('StartEnhancedMining', StartEnhancedMining)
exports('StopEnhancedMining', StopEnhancedMining)
exports('GetMiningStats', GetMiningStats)
exports('PredictNodeRespawn', PredictNodeRespawn)
exports('SetupMiningSystem', SetupMiningSystem)

-- Initialize when this file loads
CreateThread(function()
    Wait(1000) -- Wait for other systems to load
    SetupMiningSystem()
end)
