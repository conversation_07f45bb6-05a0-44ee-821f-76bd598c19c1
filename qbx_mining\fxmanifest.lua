fx_version 'cerulean'
game 'gta5'
lua54 'yes'

name 'qbx_mining'
author 'BLACKBOXAI'
version '1.0.0'
description 'Complete Mining Job System for Qbox Framework'

dependencies {
    '/server:6116',
    '/onesync',
    'ox_lib',
    'ox_inventory',
    'ox_target',
    'qbx_core'
}

shared_scripts {
    '@ox_lib/init.lua',
    'config.lua',
    'shared/bridge.lua',
    'shared/types.lua'
}

client_scripts {
    'client/main.lua',
    'client/mining.lua',
    'client/ui.lua',
    'client/peds.lua',
    'client/models.lua'
}

server_scripts {
    'server/main.lua',
    'server/mining.lua',
    'server/economy.lua',
    'server/items.lua',
    'server/persistence.lua'
}

files {
    'locales/*.json',
    'web/index.html',
    'web/app.js',
    'web/style.css'
}

ui_page 'web/index.html'

ox_libs {
    'locale',
    'table',
    'math'
}
