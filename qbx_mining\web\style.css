/* QBX Mining System - Modern Dark UI Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: #ffffff;
    overflow: hidden;
}

.mining-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 1000;
}

/* Progress Container */
.progress-container {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    min-width: 300px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-header h3 {
    color: #64ffda;
    font-size: 16px;
    font-weight: 600;
}

.progress-info {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #b0bec5;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #64ffda, #00bcd4);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #90a4ae;
}

/* Smelting Container */
.smelting-container {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    min-width: 280px;
    max-width: 350px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
}

.smelting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.smelting-header h3 {
    color: #ff9800;
    font-size: 16px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.smelting-jobs {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.smelting-job {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.job-title {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

.job-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

.job-status.completed {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

.job-status.processing {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.job-progress {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.job-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff9800, #ff5722);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* Level Up Notification */
.level-up-notification {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #64ffda;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    backdrop-filter: blur(15px);
    box-shadow: 0 16px 64px rgba(100, 255, 218, 0.3);
    pointer-events: auto;
    animation: levelUpAppear 0.5s ease-out;
}

@keyframes levelUpAppear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.level-up-content h2 {
    color: #64ffda;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 0 0 20px rgba(100, 255, 218, 0.5);
}

.level-display {
    background: linear-gradient(135deg, #64ffda, #00bcd4);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 0 30px rgba(100, 255, 218, 0.4);
}

.level-display span {
    color: #000000;
    font-size: 32px;
    font-weight: 700;
}

.level-up-content p {
    color: #b0bec5;
    font-size: 16px;
}

/* Statistics Container */
.stats-container {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    min-width: 250px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-header h3 {
    color: #9c27b0;
    font-size: 16px;
    font-weight: 600;
}

.toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.stats-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: #b0bec5;
    font-size: 14px;
}

.stat-value {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
}

/* Tool Durability */
.durability-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    min-width: 200px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
}

.durability-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.durability-header span:first-child {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

.durability-header span:last-child {
    color: #4caf50;
    font-size: 14px;
    font-weight: 600;
}

.durability-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.durability-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 3px;
    transition: all 0.3s ease;
    width: 100%;
}

.durability-fill.low {
    background: linear-gradient(90deg, #ff9800, #ff5722);
}

.durability-fill.critical {
    background: linear-gradient(90deg, #f44336, #d32f2f);
    animation: durabilityPulse 1s infinite;
}

@keyframes durabilityPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Ore Type Colors */
.ore-coal { color: #424242; }
.ore-copper { color: #ff8a65; }
.ore-iron { color: #90a4ae; }
.ore-silver { color: #e0e0e0; }
.ore-gold { color: #ffd54f; }
.ore-diamond { color: #81d4fa; }

/* Responsive Design */
@media (max-width: 768px) {
    .progress-container,
    .smelting-container,
    .stats-container,
    .durability-container {
        position: relative;
        margin: 10px;
        min-width: auto;
        width: calc(100% - 20px);
    }
    
    .mining-container {
        display: flex;
        flex-direction: column;
        padding: 10px;
    }
    
    .level-up-notification {
        width: 90%;
        max-width: 400px;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
