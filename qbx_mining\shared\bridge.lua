-- Framework Bridge for Qbox Integration
Bridge = {}

-- Get player data from framework
function Bridge.GetPlayerData()
    if GetResourceState('qbx_core') == 'started' then
        return exports.qbx_core:GetPlayerData()
    end
    return nil
end

-- Get player from server ID
function Bridge.GetPlayer(src)
    if GetResourceState('qbx_core') == 'started' then
        return exports.qbx_core:GetPlayer(src)
    end
    return nil
end

-- Add money to player
function Bridge.AddMoney(src, account, amount)
    local player = Bridge.GetPlayer(src)
    if not player then return false end
    
    if account == 'bank' then
        player.Functions.AddMoney('bank', amount)
    else
        player.Functions.AddMoney('cash', amount)
    end
    return true
end

-- Remove money from player
function Bridge.RemoveMoney(src, account, amount)
    local player = Bridge.GetPlayer(src)
    if not player then return false end
    
    if account == 'bank' then
        return player.Functions.RemoveMoney('bank', amount)
    else
        return player.Functions.RemoveMoney('cash', amount)
    end
end

-- Get player money
function Bridge.GetMoney(src, account)
    local player = Bridge.GetPlayer(src)
    if not player then return 0 end
    
    if account == 'bank' then
        return player.PlayerData.money.bank or 0
    else
        return player.PlayerData.money.cash or 0
    end
end

-- Get/Set player metadata
function Bridge.GetMetadata(src, key)
    local player = Bridge.GetPlayer(src)
    if not player then return nil end
    
    return player.PlayerData.metadata[key]
end

function Bridge.SetMetadata(src, key, value)
    local player = Bridge.GetPlayer(src)
    if not player then return false end
    
    player.Functions.SetMetaData(key, value)
    return true
end

-- Notification system
function Bridge.Notify(src, message, type, duration)
    if IsDuplicityVersion() then
        -- Server side
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Mining',
            description = message,
            type = type or 'inform',
            duration = duration or 5000
        })
    else
        -- Client side
        lib.notify({
            title = 'Mining',
            description = message,
            type = type or 'inform',
            duration = duration or 5000
        })
    end
end

-- Progress bar (client only)
function Bridge.ProgressBar(data)
    if IsDuplicityVersion() then return end
    
    return lib.progressBar({
        duration = data.duration,
        label = data.label,
        useWhileDead = false,
        canCancel = data.canCancel or false,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = data.anim,
        prop = data.prop
    })
end

-- Skill check (client only)
function Bridge.SkillCheck(difficulty, keys)
    if IsDuplicityVersion() then return end
    
    return lib.skillCheck(difficulty, keys)
end

-- Context menu (client only)
function Bridge.ShowContext(data)
    if IsDuplicityVersion() then return end
    
    lib.showContext(data.id)
end

function Bridge.RegisterContext(data)
    if IsDuplicityVersion() then return end
    
    lib.registerContext(data)
end

-- Input dialog (client only)
function Bridge.InputDialog(data)
    if IsDuplicityVersion() then return end
    
    return lib.inputDialog(data.heading, data.rows)
end

-- Alert dialog (client only)
function Bridge.AlertDialog(data)
    if IsDuplicityVersion() then return end
    
    return lib.alertDialog(data)
end

-- Check if player has permission (server only)
function Bridge.HasPermission(src, permission)
    if not IsDuplicityVersion() then return false end
    
    -- Check if player has admin permission
    local player = Bridge.GetPlayer(src)
    if not player then return false end
    
    -- Check for admin group or ace permission
    return IsPlayerAceAllowed(src, permission) or 
           (player.PlayerData.job and player.PlayerData.job.name == 'admin') or
           (player.PlayerData.metadata and player.PlayerData.metadata.isadmin)
end

-- Get player identifier
function Bridge.GetIdentifier(src)
    if not IsDuplicityVersion() then return nil end
    
    local player = Bridge.GetPlayer(src)
    if not player then return nil end
    
    return player.PlayerData.citizenid
end

-- Get player name
function Bridge.GetPlayerName(src)
    if not IsDuplicityVersion() then return 'Unknown' end
    
    local player = Bridge.GetPlayer(src)
    if not player then return 'Unknown' end
    
    return player.PlayerData.charinfo.firstname .. ' ' .. player.PlayerData.charinfo.lastname
end

return Bridge
