// QBX Mining System - Web UI JavaScript
class MiningUI {
    constructor() {
        this.isVisible = false;
        this.currentProgress = 0;
        this.playerStats = {
            level: 1,
            xp: 0,
            xpToNext: 100,
            totalOres: 0,
            sessionTime: 0
        };
        this.smeltingJobs = [];
        this.toolDurability = 100;
        this.toolName = 'Basic Pickaxe';
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.startTimers();
        
        // Listen for messages from FiveM
        window.addEventListener('message', (event) => {
            this.handleMessage(event.data);
        });
        
        console.log('QBX Mining UI initialized');
    }
    
    bindEvents() {
        // Close buttons
        document.getElementById('close-smelting')?.addEventListener('click', () => {
            this.hideSmelting();
        });
        
        // Toggle stats
        document.getElementById('toggle-stats')?.addEventListener('click', () => {
            this.toggleStats();
        });
        
        // ESC key to close UI
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.hideAll();
            }
        });
    }
    
    handleMessage(data) {
        switch (data.action) {
            case 'showMiningProgress':
                this.showMiningProgress(data.data);
                break;
            case 'updateMiningProgress':
                this.updateMiningProgress(data.progress);
                break;
            case 'hideMiningProgress':
                this.hideMiningProgress();
                break;
            case 'showLevelUp':
                this.showLevelUp(data.level);
                break;
            case 'updatePlayerStats':
                this.updatePlayerStats(data.stats);
                break;
            case 'updateSmeltingJobs':
                this.updateSmeltingJobs(data.jobs);
                break;
            case 'showSmelting':
                this.showSmelting();
                break;
            case 'hideSmelting':
                this.hideSmelting();
                break;
            case 'updateToolDurability':
                this.updateToolDurability(data.durability, data.toolName);
                break;
            case 'showStats':
                this.showStats();
                break;
            case 'hideStats':
                this.hideStats();
                break;
            case 'hideAll':
                this.hideAll();
                break;
        }
    }
    
    showMiningProgress(data) {
        const container = document.getElementById('mining-progress');
        const title = document.getElementById('progress-title');
        const ore = document.getElementById('progress-ore');
        const level = document.getElementById('progress-level');
        
        if (container && title && ore && level) {
            title.textContent = data.title || 'Mining in Progress...';
            ore.textContent = data.oreType || 'Unknown Ore';
            ore.className = `ore-${data.oreType?.toLowerCase() || 'coal'}`;
            level.textContent = `Level ${data.level || 1}`;
            
            container.style.display = 'block';
            container.classList.add('fade-in');
            
            this.currentProgress = 0;
            this.updateProgressBar(0);
        }
    }
    
    updateMiningProgress(progress) {
        this.currentProgress = Math.max(0, Math.min(100, progress));
        this.updateProgressBar(this.currentProgress);
        
        // Update time display
        const timeElement = document.getElementById('progress-time');
        if (timeElement) {
            const elapsed = Math.floor((100 - this.currentProgress) / 100 * 5); // Assuming 5 second max
            timeElement.textContent = `Time: ${elapsed}s`;
        }
    }
    
    updateProgressBar(progress) {
        const progressFill = document.getElementById('progress-fill');
        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }
    }
    
    hideMiningProgress() {
        const container = document.getElementById('mining-progress');
        if (container) {
            container.classList.add('fade-out');
            setTimeout(() => {
                container.style.display = 'none';
                container.classList.remove('fade-in', 'fade-out');
            }, 300);
        }
    }
    
    showLevelUp(level) {
        const container = document.getElementById('level-up');
        const levelDisplay = document.getElementById('new-level');
        
        if (container && levelDisplay) {
            levelDisplay.textContent = level;
            container.style.display = 'block';
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                this.hideLevelUp();
            }, 3000);
        }
    }
    
    hideLevelUp() {
        const container = document.getElementById('level-up');
        if (container) {
            container.style.display = 'none';
        }
    }
    
    updatePlayerStats(stats) {
        this.playerStats = { ...this.playerStats, ...stats };
        
        // Update XP display in progress
        const xpElement = document.getElementById('progress-xp');
        if (xpElement) {
            xpElement.textContent = `XP: ${this.playerStats.xp}/${this.playerStats.xpToNext}`;
        }
        
        // Update stats panel
        document.getElementById('stat-level').textContent = this.playerStats.level;
        document.getElementById('stat-xp').textContent = this.playerStats.xp;
        document.getElementById('stat-ores').textContent = this.playerStats.totalOres;
        
        // Update session time
        const sessionMinutes = Math.floor(this.playerStats.sessionTime / 60000);
        document.getElementById('stat-time').textContent = `${sessionMinutes}m`;
    }
    
    updateSmeltingJobs(jobs) {
        this.smeltingJobs = jobs;
        this.renderSmeltingJobs();
    }
    
    renderSmeltingJobs() {
        const container = document.getElementById('smelting-jobs');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (this.smeltingJobs.length === 0) {
            container.innerHTML = '<div class="no-jobs">No active smelting jobs</div>';
            return;
        }
        
        this.smeltingJobs.forEach(job => {
            const jobElement = this.createSmeltingJobElement(job);
            container.appendChild(jobElement);
        });
    }
    
    createSmeltingJobElement(job) {
        const jobDiv = document.createElement('div');
        jobDiv.className = 'smelting-job';
        
        const progress = job.timeRemaining > 0 ? 
            ((job.totalTime - job.timeRemaining) / job.totalTime) * 100 : 100;
        
        const statusClass = job.timeRemaining <= 0 ? 'completed' : 'processing';
        const statusText = job.timeRemaining <= 0 ? 'Completed' : 'Processing';
        
        jobDiv.innerHTML = `
            <div class="job-header">
                <span class="job-title">${job.amount}x ${job.oreType}</span>
                <span class="job-status ${statusClass}">${statusText}</span>
            </div>
            <div class="job-progress">
                <div class="job-progress-fill" style="width: ${progress}%"></div>
            </div>
        `;
        
        return jobDiv;
    }
    
    showSmelting() {
        const container = document.getElementById('smelting-progress');
        if (container) {
            container.style.display = 'block';
            container.classList.add('fade-in');
        }
    }
    
    hideSmelting() {
        const container = document.getElementById('smelting-progress');
        if (container) {
            container.classList.add('fade-out');
            setTimeout(() => {
                container.style.display = 'none';
                container.classList.remove('fade-in', 'fade-out');
            }, 300);
        }
    }
    
    updateToolDurability(durability, toolName) {
        this.toolDurability = Math.max(0, Math.min(100, durability));
        this.toolName = toolName || this.toolName;
        
        const nameElement = document.getElementById('tool-name');
        const percentElement = document.getElementById('durability-percent');
        const fillElement = document.getElementById('durability-fill');
        
        if (nameElement) nameElement.textContent = this.toolName;
        if (percentElement) percentElement.textContent = `${Math.round(this.toolDurability)}%`;
        
        if (fillElement) {
            fillElement.style.width = `${this.toolDurability}%`;
            
            // Update color based on durability
            fillElement.classList.remove('low', 'critical');
            if (this.toolDurability <= 10) {
                fillElement.classList.add('critical');
            } else if (this.toolDurability <= 30) {
                fillElement.classList.add('low');
            }
        }
        
        // Show/hide durability display
        const container = document.getElementById('tool-durability');
        if (container) {
            container.style.display = this.toolDurability > 0 ? 'block' : 'none';
        }
    }
    
    showStats() {
        const container = document.getElementById('mining-stats');
        if (container) {
            container.style.display = 'block';
            container.classList.add('fade-in');
        }
    }
    
    hideStats() {
        const container = document.getElementById('mining-stats');
        if (container) {
            container.classList.add('fade-out');
            setTimeout(() => {
                container.style.display = 'none';
                container.classList.remove('fade-in', 'fade-out');
            }, 300);
        }
    }
    
    toggleStats() {
        const container = document.getElementById('mining-stats');
        const button = document.getElementById('toggle-stats');
        
        if (container && button) {
            const isVisible = container.style.display !== 'none';
            
            if (isVisible) {
                this.hideStats();
                button.textContent = 'Show';
            } else {
                this.showStats();
                button.textContent = 'Hide';
            }
        }
    }
    
    hideAll() {
        this.hideMiningProgress();
        this.hideSmelting();
        this.hideStats();
        this.hideLevelUp();
        
        // Send message to FiveM that UI was closed
        this.sendMessage('uiClosed');
    }
    
    startTimers() {
        // Update smelting job progress every second
        setInterval(() => {
            if (this.smeltingJobs.length > 0) {
                let updated = false;
                
                this.smeltingJobs.forEach(job => {
                    if (job.timeRemaining > 0) {
                        job.timeRemaining = Math.max(0, job.timeRemaining - 1);
                        updated = true;
                    }
                });
                
                if (updated) {
                    this.renderSmeltingJobs();
                }
            }
        }, 1000);
        
        // Update session time every minute
        setInterval(() => {
            this.playerStats.sessionTime += 60000;
            this.updatePlayerStats(this.playerStats);
        }, 60000);
    }
    
    sendMessage(action, data = {}) {
        fetch(`https://${GetParentResourceName()}/${action}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        }).catch(err => {
            // Ignore fetch errors in development
            console.log('Fetch error (normal in development):', err);
        });
    }
    
    // Utility functions
    formatTime(seconds) {
        if (seconds < 60) {
            return `${seconds}s`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}m ${remainingSeconds}s`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
    }
    
    formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
}

// Initialize the UI when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.miningUI = new MiningUI();
});

// Helper function for FiveM resource name
function GetParentResourceName() {
    return 'qbx_mining';
}

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MiningUI;
}
